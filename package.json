{"name": "flowai", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"@heroicons/vue": "^2.1.3", "@microsoft/clarity": "^1.0.0", "@monaco-editor/loader": "^1.4.0", "@vue-flow/background": "^1.3.0", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.41.2", "@vue-flow/minimap": "^1.5.0", "@vueuse/motion": "^2.1.0", "axios": "^1.6.8", "lodash-es": "^4.17.21", "md-editor-v3": "^5.0.0", "monaco-editor": "^0.50.0", "notiwind": "^2.0.2", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-i18n": "^10.0.5", "vue-monaco-editor": "^0.0.19", "vue-router": "^4.3.0", "vue3-iframe": "^0.0.7", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/lodash-es": "^4.17.12", "@types/node": "^20.12.5", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "daisyui": "^4.10.1", "npm-run-all2": "^6.1.2", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "~5.4.0", "vite": "^5.2.8", "vue-tsc": "^2.0.11"}}