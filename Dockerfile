# 使用Node.js官方镜像作为基础镜像
FROM node:20-alpine AS build-stage

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json（如果存在）
COPY package*.json ./

# 安装项目依赖
RUN npm install

# 复制项目文件
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:stable-alpine AS production-stage

# 从构建阶段复制构建后的文件到nginx
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 添加自定义的nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露80端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]