import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";

// https://vitejs.dev/config/
export default defineConfig({
	base: "/dashboard/", // 添加这一行
	plugins: [vue(), vueJsx()],
	resolve: {
		alias: {
			"@": fileURLToPath(new URL("./src", import.meta.url)),
		},
	},
	server: {
		proxy: {
			"/v1": {
				target: "http://localhost:8999",
				changeOrigin: true,
			},
		},
	},

	experimental: {
		// renderBuiltUrl(filename, { type }) {
		// 	// console.log(filename, type);
		// 	if (type === "asset") {
		// 		return `https://static.flowai.cc/${filename.replace(/^assets\//, "")}`;
		// 	}
		// 	return filename;
		// },
	},
});
