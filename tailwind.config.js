/** @type {import('tailwindcss').Config} */
export default {
	purge: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
	content: [],
	theme: {
		extend: {},
	},
	plugins: [require("daisyui")],
	daisyui: {
		themes: [
			{
				light: {
					primary: "#1777fd",
					"primary-content": "#ffffff",
					secondary: "#29e8e9",
					accent: "#00b400",
					neutral: "#1f1f1f",
					"base-100": "#fffdfd",
					info: "#00fcff",
					success: "#22c55d",
					warning: "#ff7100",
					error: "#dd4d57",
					"error-content": "#ffffff",
				},
			},
		],
	},
};
