import { ref, onMounted } from "vue";
import { Nodes, GetNode } from "@/components/nodes/nodes.ts";
import { getPlugins } from "@/api/plugin";
import { useI18n } from "vue-i18n";
export type NodeOption = {
	label: string;
	nodeShowName: string;
	type: string;
	plugin?: any;
	icon?: any;
	group: string;
	config?: any[];
	output?: any[];
};
export function useNodeOptions(filter?: (type: string) => boolean) {
	const options = ref<NodeOption[]>([]);
	const { t } = useI18n();
	// 获取基础节点
	const loadBaseNodes = () => {
		const baseNodes = Object.keys(Nodes)
			.filter((i) => i !== "in")
			.filter((i) => Nodes[i].show)
			.filter((i) => (filter ? filter(i) : true))
			.map((i) => {
				const node = GetNode(i, t);
				return {
					label: node.label,
					nodeShowName: node.nodeShowName,
					type: node.type,
					plugin: null,
					icon: node.icon,
					group: node.group,
					config: node.config,
					output: node.output,
				};
			});

		options.value = baseNodes;
	};

	// 加载插件节点
	const loadPluginNodes = async () => {
		try {
			const list = await getPlugins();
			const pluginNodes = (list || []).map((res) => {
				const plugin = JSON.parse(res.proto) || {};
				return {
					label: plugin.label,
					nodeShowName: plugin.nodeShowName || plugin.label,
					type: "plugin",
					plugin: plugin,
					icon: Nodes["plugin"].icon,
					group: t("nodes.group.plugin"),
					config: plugin.config,
					output: plugin.output,
				};
			});
			options.value = [...options.value, ...pluginNodes];
		} catch (error) {
			console.error("Failed to load plugins:", error);
		}
	};

	onMounted(() => {
		loadBaseNodes();
		if (!filter) {
			loadPluginNodes();
		}
	});

	return {
		options,
	};
}
