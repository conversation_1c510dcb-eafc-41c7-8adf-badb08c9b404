// src/api/axios.js

import axios from "axios";
import { BaseURL } from "./base";

const instance = axios.create({
	baseURL: BaseURL, // 设置基础URL
	timeout: 30000, // 设置请求超时时间
	// 其他配置...
});
// 添加响应拦截器
instance.interceptors.response.use(
	(response) => {
		// 对响应数据做些什么
		return response.data;
	},
	(error) => {
		// 响应错误时做些什么
		if (error.response) {
			// 请求已发出，但服务器响应了错误的状态码
			// console.error("Error status:", error.response);
			return Promise.reject(error.response.data.msg);
		} else if (error.request) {
			// 请求已发出，但没有收到任何响应
			console.error("Error request:", error.request);
		} else {
			// 设置请求时发生了错误
			console.error("Error message:", error.message);
		}
		return Promise.reject(error);
	}
);

export default instance;
