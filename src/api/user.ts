import axios from "@/api/axios";

export type User = {
	id: number;
	username: string;
	email: string;
	password: string;
	credits: number;
};

export async function login(usernameOrEmail: string, password: string) {
	return axios.post("/login", {
		username_or_email: usernameOrEmail,
		password,
	});
}

export async function me() {
	let res = await axios.get("/me");
	return res.data as User;
}

export type userUpdate = {
	phone?: string;
	old_password?: string;
	new_password?: string;
};
export async function updateMe(user: userUpdate) {
	return await axios.post("/user/settings", user);
}

export function logout() {
	return axios.post("/logout");
}

export function googleLogin() {
	return axios.get("/auth/google");
}

export function githubLogin() {
	return axios.get("/auth/github");
}

export function signUp(email: string, password: string, lang: string) {
	return axios.post("/register", {
		email: email,
		password: password,
		lang: lang,
	});
}

export function verifyEmail(email: string, code: string) {
	return axios.post("/verify_email", {
		email: email,
		code: code,
	});
}

export function resendVerifyCode(email: string, lang: string) {
	return axios.post("/resend_verify_code", {
		email: email,
		lang: lang,
	});
}
