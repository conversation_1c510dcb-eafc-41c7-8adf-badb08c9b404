import axios from "@/api/axios";
import type { Project } from "./projects";

export type WorkflowLog = {
	uuid: string; // 新增 uuid 字段
	created_at: string;
	workflow_id: number;
	input: string;
	output: string;
	logs: string;
	user_id: number;
	workflow?: Omit<Project, "data">; // 去掉 data 字段
	total_time?: number;
	error?: string;
};

export type WorkflowLogs = {
	total: number;
	list: WorkflowLog[];
};

export async function getWorkflowLogs(page = 1, pageSize = 10) {
	let res = await axios.get<WorkflowLogs>(
		`/workflow/logs?page=${page}&pageSize=${pageSize}`
	);
	return res.data;
}

export async function getWorkflowLog(uuid: string) {
	let res = await axios.get<WorkflowLog>(`/workflow/logs/${uuid}`);
	return res.data;
}

export async function deleteWorkflowLog(uuid: string) {
	await axios.delete(`/workflow/logs/${uuid}`);
}
