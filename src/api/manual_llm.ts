import axios from "@/api/axios";

export type ManualLLM = {
	uuid: string;
	model_name: string;
	model_type: string;
	model_endpoint: string;
	model_key: string;
	content_limit: number;
	support_function_call: boolean;
	support_json_output: boolean;
	support_vision: boolean;
};

export type ManualLLMListResponse = {
	manual_llms: ManualLLM[];
};

export type OfficialLLMListResponse = {
	official_llms: ManualLLM[];
};

export type ManualLLMResponse = {
	manual_llm: ManualLLM;
};

export async function getManualLLMs(page: number = 1, pageSize: number = 10) {
	const res = await axios.get<ManualLLMListResponse>(`/manual_llms`, {
		params: { page, pageSize },
	});
	return res.data;
}

export async function official_llms() {
	const res = await axios.get<OfficialLLMListResponse>(`/official_llms`);
	return res.data;
}

export async function createManualLLM(data: Omit<ManualLLM, "uuid" | "user_id" | "user">) {
	const res = await axios.post<ManualLLMResponse>(`/manual_llms`, data);
	return res.data;
}

export async function updateManualLLM(
	uuid: string,
	data: Partial<Omit<ManualLLM, "uuid" | "user_id" | "user">>
) {
	const res = await axios.patch<ManualLLMResponse>(`/manual_llms/${uuid}`, data);
	return res.data;
}

export async function deleteManualLLM(uuid: string) {
	const res = await axios.delete<{ message: string }>(`/manual_llms/${uuid}`);
	return res.data;
}

export async function getManualLLMDetails(uuid: string) {
	const res = await axios.get<ManualLLMResponse>(`/manual_llms/${uuid}`);
	return res.data;
}
