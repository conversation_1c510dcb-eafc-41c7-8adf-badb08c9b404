import axios from "@/api/axios";

export type OrderResponse = {
	order_no: string;
	amount: number;
	points: number;
	status: string;
	paid_at: string;
	created_at: string;
	payment_url: string;
};

export async function getOrder(order_no: string) {
	let res = await axios.get<OrderResponse>(`/orders/${order_no}`);
	return res.data;
}

export type OrderCreateResponse = {
	order_no: string;
	amount: number;
	points: number;
	payment_url: string;
};

export async function createOrder(points: number, provider: string) {
	let res = await axios.post<OrderCreateResponse>(`/order/create`, { points, provider });
	return res.data;
}
