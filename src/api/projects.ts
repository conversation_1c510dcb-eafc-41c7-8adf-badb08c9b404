import axios from "@/api/axios";

export type Project = {
	uuid: string;
	name: string;
	description: string;
	data: string;
	created_at: string;
	is_published?: boolean;
};

export async function getProjects(page = 1, pageSize = 10, search = "") {
	let res = await axios.get(`/projects?page=${page}&pageSize=${pageSize}&search=${search}`);
	return res.data;
}

export async function getProject(uuid: string) {
	let res = await axios.get<Project>(`/projects/${uuid}`);
	return res.data;
}

export async function createProject(project: Project): Promise<string> {
	let res = await axios.post("/projects", project);
	return res.data;
}

export async function updateProject(uuid: string, project: Project) {
	return await axios.patch(`/projects/${uuid}`, project);
}

export async function deleteProject(uuid: string) {
	return await axios.delete(`/projects/${uuid}`);
}

export async function cloneProject(uuid: string) {
	return await axios.post(`/projects_clone/${uuid}`);
}

export type PublishedVersion = {
	id: string;
	name: string;
	version: string;
	is_active: boolean;
	updated_at: string;
};

export async function publishedVersions(uuid: string) {
	return await axios.get(`/projects/${uuid}/published_versions`);
}
