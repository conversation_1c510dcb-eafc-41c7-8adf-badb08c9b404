import axios from "@/api/axios";

export async function publishWorkflow(
	workflowId: string,
	name: string,
	version: string,
	description: string
) {
	let res = await axios.post(`/published_workflows`, {
		id: workflowId,
		name,
		version,
		description,
	});
	return res.data;
}

export interface PublishedWorkflowItem {
	uuid: string;
	version: string;
	name: string;
	description: string;
	is_active: boolean;
}

export interface PublishedWorkflow {
	total: number;
	list: PublishedWorkflowItem[];
}

export async function listPublishedWorkflows() {
	let res = await axios.get<PublishedWorkflow>(`/published_workflows`);
	return res.data;
}

export async function getPublishedWorkflow(workflowId: string) {
	let res = await axios.get(`/published_workflows/${workflowId}`);
	return res.data;
}

export async function listPublishedWorkflowVersions(workflowId: string) {
	let res = await axios.get(`/published_workflows/${workflowId}/versions`);
	return res.data;
}

export async function updatePublishedWorkflowStatus(workflowId: string, status: boolean) {
	let res = await axios.patch(`/published_workflows/${workflowId}/status`, {
		is_active: status,
	});
	return res.data;
}

export async function deletePublishedWorkflow(workflowId: string) {
	let res = await axios.delete(`/published_workflows/${workflowId}`);
	return res.data;
}
