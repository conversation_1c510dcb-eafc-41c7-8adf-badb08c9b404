import axios from "@/api/axios";

export interface APIKey {
	id: number;
	name: string;
	key: string;
	created_at: string;
	expires_at: string;
	last_used: string;
}

interface CreateAPIKeyRequest {
	name: string;
	expires_in_days?: number;
}

export async function listApiKeys(): Promise<APIKey[]> {
	let res = await axios.get(`/api_keys`);
	return res.data;
}

export async function createApi<PERSON>ey(data: CreateAPIKeyRequest) {
	let res = await axios.post(`/api_keys`, data);
	return res.data;
}

export async function deleteApiKey(id: number): Promise<void> {
	await axios.delete(`/api_keys/${id}`);
}
