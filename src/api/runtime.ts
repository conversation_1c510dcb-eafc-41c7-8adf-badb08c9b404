import axios from "@/api/axios";
import streamReq from "@/api/stream";

export type RuntimeRes = {
	output: string;
	total_time: number;
};

export async function ProjectRuntime(
	uuid: string,
	input: object,
	onEvent: (event: string, data: any) => void
) {
	return streamReq(`/workflow/runtime/project/${uuid}`, {}, { input: input }, onEvent);
}

export async function ProjectDebug(
	input: object,
	data: string,
	onEvent: (event: string, data: any) => void
) {
	return streamReq("/workflow/runtime/debug", {}, { input: input, data: data }, onEvent);
}
