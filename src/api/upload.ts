import axios from "@/api/axios";

interface UploadResponse {
	code: number;
	data: string | null;
	msg?: string;
}

/**
 * 上传图片
 * @param file 图片文件
 * @returns 上传成功后的图片URL
 * @throws Error 当上传失败时抛出错误
 */
export async function uploadImage(file: File): Promise<string> {
	const formData = new FormData();
	formData.append("image", file);

	const response: UploadResponse = await axios.post("/upload/image", formData, {
		headers: {
			"Content-Type": "multipart/form-data",
		},
	});

	if (response.code === 0 && response.data) {
		return response.data;
	}

	throw new Error(response.msg || "上传失败");
}
