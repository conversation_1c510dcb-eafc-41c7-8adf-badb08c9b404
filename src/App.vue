<script setup lang="ts">
	import { RouterLink, RouterView, useRoute, useRouter } from "vue-router";
	import { useCurNav } from "@/stores/curNav";
	import Dashboard from "@/components/dashboard.vue";
	import { useUserStore } from "./stores/user";
	import { me } from "./api/user";
	import { ref, defineAsyncComponent } from "vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const navStore = useCurNav();
	const route = useRoute();
	const router = useRouter();
	const userStore = useUserStore();
	const loading = ref(false);
	const MyNotify = defineAsyncComponent(() => import("@/components/utils/notify.vue"));
	// 路由拦截
	router.beforeEach((to, from, next) => {
		if (to.meta.requiresAuth) {
			if (userStore.userRef != null && Object.keys(userStore.userRef).length > 0) {
				next();
			} else {
				loading.value = true;
				me()
					.then((user) => {
						userStore.setUser(user);
						next();
					})
					.catch(() => {
						next({ name: "login" });
					})
					.finally(() => {
						loading.value = false;
					});
			}
		} else {
			next();
		}
	});
	router.afterEach(() => {
		// set title
		if (route.meta.title) {
			document.title = t(route.meta.title as string) + " - FlowAI";
		} else {
			document.title = "FlowAI";
		}
	});
</script>

<template>
	<div
		v-if="loading"
		class="fixed top-0 left-0 right-0 bottom-0 bg-white z-50 flex flex-col justify-center items-center"
	>
		<div class="loading loading-ring loading-lg"></div>
	</div>
	<MyNotify></MyNotify>
	<div v-if="route.meta.inDashboard">
		<Dashboard :current="navStore.curNav">
			<RouterView v-motion-slide-right />
		</Dashboard>
	</div>
	<div v-else>
		<RouterView />
	</div>
</template>

<style scoped></style>
