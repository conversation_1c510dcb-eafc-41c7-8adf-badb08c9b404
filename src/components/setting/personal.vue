<script setup lang="ts">
	import { ref, computed } from "vue";
	import { useUserStore } from "@/stores/user";
	import { updateMe, type userUpdate } from "@/api/user";
	import { createOrder, type OrderCreateResponse } from "@/api/order";
	import { Error, Success, Info } from "@/utils/notify";
	import { useI18n } from "vue-i18n";

	const userStore = useUserStore();
	const { t } = useI18n();

	const username = ref(userStore.userRef?.username);
	const email = ref(userStore.userRef?.email);
	// @ts-ignore
	const phone = ref(userStore.userRef?.phone);
	const newPassword = ref("");
	const oldPassword = ref("");
	const credits = ref(userStore.userRef?.credits || 0);

	// Check if user is using third-party login (Google or Github)
	const isThirdPartyLogin = computed(() => {
		return username.value?.startsWith("Google_") || username.value?.startsWith("Github_");
	});

	const isLoading = ref(false);
	const isRechargeLoading = ref(false);
	const isRedirecting = ref(false);
	const showRechargeDialog = ref(false);

	const handleRecharge = async () => {
		showRechargeDialog.value = true;
	};

	const confirmRecharge = async () => {
		if (isRechargeLoading.value) return;

		try {
			isRechargeLoading.value = true;
			const response = await createOrder(600, "creem");
			if (response?.payment_url) {
				showRechargeDialog.value = false;
				isRedirecting.value = true;
				window.location.href = response.payment_url;
			} else {
				Error(t("error"), t("payment_error"));
				showRechargeDialog.value = false;
			}
		} catch (err) {
			Error(t("error"), t("payment_error"));
			showRechargeDialog.value = false;
		} finally {
			isRechargeLoading.value = false;
		}
	};

	const updateUserInfo = () => {
		// For third-party login users, only check phone field
		if (isThirdPartyLogin.value) {
			if (phone.value == "") {
				Error(t("error"), t("no_changes"));
				return;
			}
		} else {
			// For regular users, check all fields
			if (
				// email.value == "" &&
				phone.value == "" &&
				newPassword.value == "" &&
				oldPassword.value == ""
			) {
				Error(t("error"), t("no_changes"));
				return;
			}
			if (newPassword.value != "" && oldPassword.value == "") {
				Error(t("error"), t("must_enter_old_password"));
				return;
			}
		}

		let request: userUpdate = {
			phone: phone.value,
		};

		// Only include password fields for non-third-party login users
		if (!isThirdPartyLogin.value) {
			request.new_password = newPassword.value;
			request.old_password = oldPassword.value;
		}

		isLoading.value = true;
		updateMe(request)
			.then(() => {
				Success(t("success"), t("update_success"));
			})
			.catch((err) => {
				Error(t("error"), t("update_error"));
			})
			.finally(() => {
				isLoading.value = false;
			});
	};
</script>

<template>
	<div class="relative">
		<!-- 支付跳转加载层 -->
		<Teleport to="body">
			<div
				v-if="isRedirecting"
				class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
			>
				<div class="bg-white p-8 rounded-lg text-center">
					<div class="loading loading-spinner loading-lg mb-4"></div>
					<p class="text-lg font-medium">{{ t("redirecting_to_payment") }}</p>
				</div>
			</div>
		</Teleport>

		<!-- 积分部分 -->
		<div class="mb-6">
			<h3 class="text-lg font-medium mb-2">{{ t("credits") }}</h3>
			<div class="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
				<span class="text-lg font-medium text-primary">{{
					t("current_credits")
				}}</span>
				<div class="flex items-center gap-4">
					<span class="text-2xl font-bold text-primary">{{ credits }}</span>
					<button
						type="button"
						class="btn btn-primary btn-sm"
						@click="handleRecharge"
					>
						{{ t("recharge") }}
					</button>
				</div>
			</div>
		</div>

		<!-- 充值确认对话框 -->
		<Teleport to="body">
			<div class="modal" :class="{ 'modal-open': showRechargeDialog }">
				<div class="modal-box relative">
					<button
						class="btn btn-sm btn-circle absolute right-2 top-2"
						@click="showRechargeDialog = false"
					>
						✕
					</button>
					<h3 class="font-bold text-lg mb-4">{{ t("recharge_confirmation") }}</h3>
					<div class="bg-blue-50 p-4 rounded-lg mb-4">
						<div class="flex justify-between items-center mb-2">
							<span>{{ t("points_to_add") }}</span>
							<span class="text-xl font-bold text-primary">600</span>
						</div>
						<div class="flex justify-between items-center">
							<span>{{ t("price") }}</span>
							<span class="text-xl font-bold text-primary">$4.99</span>
						</div>
					</div>
					<div class="modal-action">
						<button class="btn btn-ghost" @click="showRechargeDialog = false">
							{{ t("cancel") }}
						</button>
						<button
							class="btn btn-primary"
							@click="confirmRecharge"
							:disabled="isRechargeLoading"
						>
							<span v-if="!isRechargeLoading">{{ t("confirm_payment") }}</span>
							<span v-else class="loading loading-spinner loading-sm"></span>
						</button>
					</div>
				</div>
				<div class="modal-backdrop" @click="showRechargeDialog = false"></div>
			</div>
		</Teleport>

		<!-- 用户信息部分 -->
		<div class="mb-6">
			<h3 class="text-lg font-medium mb-2">{{ t("user_info") }}</h3>
			<div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
				<span class="text-gray-500">{{ t("user_identifier") }}</span>
				<span class="text-gray-500">{{ username }}</span>
			</div>
		</div>

		<form @submit.prevent="updateUserInfo" class="space-y-6">
			<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
				<div>
					<label for="email" class="input-label">{{ t("email") }}</label>
					<input
						id="email"
						type="email"
						v-model="email"
						:placeholder="t('enter_email')"
						class="input input-bordered w-full"
						disabled
						:title="t('email_function_coming_soon')"
					/>
				</div>
				<div>
					<label for="phone" class="input-label">{{ t("phone") }}</label>
					<input
						id="phone"
						type="text"
						v-model="phone"
						:placeholder="t('enter_phone')"
						class="input input-bordered w-full"
					/>
				</div>

				<div v-if="!isThirdPartyLogin">
					<label for="newPassword" class="input-label">{{
						t("new_password")
					}}</label>
					<input
						id="newPassword"
						type="password"
						v-model="newPassword"
						:placeholder="t('enter_new_password')"
						class="input input-bordered w-full"
					/>
				</div>
				<div v-if="!isThirdPartyLogin">
					<label for="oldPassword" class="input-label">{{
						t("old_password")
					}}</label>
					<input
						id="oldPassword"
						type="password"
						v-model="oldPassword"
						:placeholder="t('enter_old_password')"
						class="input input-bordered w-full"
					/>
				</div>
			</div>
			<div class="flex justify-start">
				<button type="submit" class="btn btn-primary" :disabled="isLoading">
					<span v-if="!isLoading">{{ t("save_settings") }}</span>
					<span v-else class="loading loading-spinner loading-sm"></span>
				</button>
			</div>
		</form>
	</div>
</template>
