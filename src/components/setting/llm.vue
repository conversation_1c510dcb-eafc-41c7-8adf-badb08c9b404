<script setup lang="ts">
	import { ref, onMounted, computed, reactive } from "vue";
	import { Error, Success } from "@/utils/notify";
	import {
		getManualLLMs,
		createManualLLM,
		deleteManualLLM,
		updateManualLLM,
		type ManualLLM,
	} from "@/api/manual_llm";
	import { useI18n } from "vue-i18n";
	import { PlusIcon, PencilIcon, TrashIcon, XMarkIcon } from "@heroicons/vue/24/outline";
	import DeleteConfirmDialog from "@/components/common/DeleteConfirmDialog.vue";

	const { t } = useI18n();

	// 状态管理
	const manualLLMs = ref<ManualLLM[]>([]);
	const loadingState = reactive({
		isLoading: false,
		isAdding: false,
		isSaving: false,
		isDeleting: false,
	});

	// Dialog状态
	const showAddDialog = ref(false);
	const showEditDialog = ref(false);
	const showDeleteDialog = ref(false);
	const deleteTarget = ref<ManualLLM | null>(null);

	// 模型配置
	const modelProtocols = [
		{ value: "OpenAI", label: "OpenAI" },
		{ value: "Anthropic", label: "Anthropic" },
	];

	// 默认LLM配置
	const defaultLLM = {
		model_name: "",
		model_type: "OpenAI",
		model_endpoint: "https://api.openai.com/v1",
		model_key: "",
		content_limit: 1024 * 8,
		support_function_call: false,
		support_json_output: false,
		support_vision: false,
	};

	// 新建和编辑状态
	const newLLM = ref<Omit<ManualLLM, "uuid">>(structuredClone(defaultLLM));
	const editLLM = ref<ManualLLM | null>(null);

	// 高级选项展开状态
	const showAdvancedOptions = ref(true);
	const showEditAdvancedOptions = ref(true);

	// 计算属性：内容限制（KB）
	const contentLimitInK = computed({
		get: () => newLLM.value.content_limit / 1024,
		set: (val: number) => (newLLM.value.content_limit = val * 1024),
	});

	const editContentLimitInK = computed({
		get: () => {
			if (!editLLM.value) return 0;
			return editLLM.value.content_limit / 1024;
		},
		set: (val: number) => {
			if (editLLM.value) {
				editLLM.value.content_limit = val * 1024;
			}
		},
	});

	// 生命周期钩子
	onMounted(async () => {
		await loadManualLLMs();
	});

	// 数据加载
	const loadManualLLMs = async () => {
		loadingState.isLoading = true;
		try {
			const response = await getManualLLMs();
			manualLLMs.value = response.manual_llms;
		} catch (err) {
			Error(t("error"), t("get_custom_llm_error"));
		} finally {
			loadingState.isLoading = false;
		}
	};

	// Dialog相关函数
	const openAddDialog = () => {
		resetNewLLM();
		showAddDialog.value = true;
	};

	const closeAddDialog = () => {
		showAddDialog.value = false;
		resetNewLLM();
	};

	const openEditDialog = (llm: ManualLLM) => {
		editLLM.value = { ...llm };
		showEditAdvancedOptions.value = true;
		showEditDialog.value = true;
	};

	const closeEditDialog = () => {
		showEditDialog.value = false;
		editLLM.value = null;
		showEditAdvancedOptions.value = false;
	};

	const openDeleteDialog = (llm: ManualLLM) => {
		deleteTarget.value = llm;
		showDeleteDialog.value = true;
	};

	const closeDeleteDialog = () => {
		showDeleteDialog.value = false;
		deleteTarget.value = null;
	};

	// 添加LLM
	const addManualLLM = async () => {
		if (loadingState.isAdding) return;
		loadingState.isAdding = true;
		try {
			await createManualLLM(newLLM.value);
			Success(t("success"), t("add_custom_llm_success"));
			await loadManualLLMs();
			closeAddDialog();
		} catch (err) {
			Error(t("error"), t("add_custom_llm_error"));
		} finally {
			loadingState.isAdding = false;
		}
	};

	// 删除LLM
	const confirmDeleteLLM = async () => {
		if (!deleteTarget.value) return;

		loadingState.isDeleting = true;
		try {
			await deleteManualLLM(deleteTarget.value.uuid);
			Success(t("success"), t("delete_custom_llm_success"));
			manualLLMs.value = manualLLMs.value.filter(
				(llm) => llm.uuid !== deleteTarget.value!.uuid
			);
			closeDeleteDialog();
		} catch (err) {
			Error(t("error"), t("delete_custom_llm_error"));
		} finally {
			loadingState.isDeleting = false;
		}
	};

	// 保存编辑
	const saveEdit = async () => {
		if (!editLLM.value || loadingState.isSaving) return;
		loadingState.isSaving = true;
		try {
			await updateManualLLM(editLLM.value.uuid, editLLM.value);
			Success(t("success"), t("update_custom_llm_success"));
			await loadManualLLMs();
			closeEditDialog();
		} catch (err) {
			Error(t("error"), t("update_custom_llm_error"));
		} finally {
			loadingState.isSaving = false;
		}
	};

	// 重置新LLM表单
	const resetNewLLM = () => {
		newLLM.value = structuredClone(defaultLLM);
		showAdvancedOptions.value = true;
	};
</script>
<template>
	<div class="llm-config-container">
		<!-- 页面标题和添加按钮 -->
		<div class="flex justify-between items-center mb-8">
			<div>
				<p class="text-slate-600 mt-2 text-lg">
					{{ t("manage_your_custom_llm_models") }}
				</p>
			</div>
			<button @click="openAddDialog" class="btn btn-primary btn-md gap-2">
				<PlusIcon class="w-5 h-5" />
				{{ t("add_custom_llm") }}
			</button>
		</div>

		<!-- 加载状态 -->
		<div v-if="loadingState.isLoading" class="flex justify-center my-12">
			<span class="loading loading-spinner loading-lg text-blue-500"></span>
		</div>

		<!-- 模型列表 -->
		<div v-else class="llm-list">
			<!-- 无模型提示 -->
			<div v-if="manualLLMs.length === 0" class="empty-state">
				<div
					class="text-center p-12 bg-white/80 backdrop-blur-sm rounded-3xl border border-slate-200 shadow-xl"
				>
					<div
						class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center shadow-lg"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-12 w-12 text-blue-500"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
							/>
						</svg>
					</div>
					<h3 class="text-2xl font-bold mb-3 text-slate-800">
						{{ t("no_custom_llms") }}
					</h3>
					<p class="text-slate-500 mb-8 max-w-md mx-auto text-lg">
						{{ t("no_custom_llms_desc") }}
					</p>
					<button
						@click="openAddDialog"
						class="btn bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-0 text-white shadow-lg hover:shadow-xl transition-all duration-300 gap-2 px-8"
					>
						<PlusIcon class="w-5 h-5" />
						{{ t("add_custom_llm") }}
					</button>
				</div>
			</div>

			<!-- 模型卡片列表 -->
			<div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
				<div
					v-for="llm in manualLLMs"
					:key="llm.uuid"
					class="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-200 hover:border-blue-300 hover:-translate-y-1"
				>
					<!-- 卡片内容 -->
					<div class="p-6">
						<!-- 模型标题与类型 -->
						<div class="flex justify-between items-start mb-4">
							<div>
								<h3
									class="text-xl font-bold text-slate-800 mb-2 group-hover:text-blue-600 transition-colors"
								>
									{{ llm.model_name }}
								</h3>
								<div
									class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 border border-blue-200"
								>
									{{ llm.model_type }}
								</div>
							</div>
						</div>

						<!-- 模型详情 -->
						<div class="space-y-3 text-sm">
							<div class="flex items-center text-slate-600">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-4 w-4 mr-3 text-blue-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"
									/>
								</svg>
								<span
									class="truncate font-mono text-xs bg-slate-100 px-2 py-1 rounded"
									>{{ llm.model_endpoint }}</span
								>
							</div>
							<div class="flex items-center text-slate-600">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-4 w-4 mr-3 text-blue-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
									/>
								</svg>
								<span class="font-medium"
									>{{ llm.content_limit / 1024 }}K
									{{ t("context_limit") }}</span
								>
							</div>
						</div>

						<!-- 功能标签 -->
						<div class="mt-4 min-h-[2.5rem] flex flex-wrap gap-2 items-start">
							<div
								v-if="llm.support_function_call"
								class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200"
							>
								{{ t("function_call") }}
							</div>
							<div
								v-if="llm.support_json_output"
								class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-cyan-100 text-cyan-700 border border-cyan-200"
							>
								{{ t("json_output") }}
							</div>
							<div
								v-if="llm.support_vision"
								class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-700 border border-amber-200"
							>
								{{ t("vision") }}
							</div>
							<!-- 如果没有任何功能标签，显示占位符 -->
							<div
								v-if="
									!llm.support_function_call &&
									!llm.support_json_output &&
									!llm.support_vision
								"
								class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-500 border border-slate-200"
							>
								{{ t("no_special_features") }}
							</div>
						</div>

						<!-- 操作按钮 -->
						<div class="flex justify-end mt-6 gap-3">
							<button
								@click="openEditDialog(llm)"
								class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 gap-2"
							>
								<PencilIcon class="w-4 h-4" />
								{{ t("edit") }}
							</button>
							<button
								@click="openDeleteDialog(llm)"
								class="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 hover:border-red-300 transition-all duration-200 gap-2"
							>
								<TrashIcon class="w-4 h-4" />
								{{ t("delete") }}
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 添加LLM Dialog -->
		<Teleport to="body">
			<div v-if="showAddDialog" class="modal modal-open">
				<div
					class="modal-box max-w-3xl bg-white/95 backdrop-blur-sm border border-slate-200 shadow-2xl"
				>
					<div class="flex justify-between items-center mb-8">
						<div>
							<h3 class="text-3xl font-bold">
								{{ t("add_custom_llm") }}
							</h3>
							<p class="text-slate-600 mt-1">
								{{ t("configure_new_llm_model") }}
							</p>
						</div>
						<button
							@click="closeAddDialog"
							class="btn btn-sm btn-circle btn-ghost hover:bg-slate-100"
						>
							<XMarkIcon class="w-5 h-5" />
						</button>
					</div>

					<form @submit.prevent="addManualLLM" class="space-y-6">
						<!-- 基本信息 -->
						<div
							class="bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl p-6 border border-slate-200"
						>
							<h4
								class="font-bold mb-6 text-xl text-slate-800 flex items-center"
							>
								<div
									class="w-2 h-6 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full mr-3"
								></div>
								{{ t("basic_info") }}
							</h4>

							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div class="form-control">
									<label class="label">
										<span
											class="label-text font-semibold text-slate-700"
											>{{ t("model_name") }}</span
										>
									</label>
									<input
										v-model="newLLM.model_name"
										type="text"
										class="input input-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white"
										required
									/>
								</div>

								<div class="form-control">
									<label class="label">
										<span
											class="label-text font-semibold text-slate-700"
											>{{ t("model_protocol") }}</span
										>
									</label>
									<select
										v-model="newLLM.model_type"
										class="select select-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white"
										required
									>
										<option
											v-for="protocol in modelProtocols"
											:key="protocol.value"
											:value="protocol.value"
										>
											{{ protocol.label }}
										</option>
									</select>
								</div>
							</div>

							<div class="form-control mt-6">
								<label class="label">
									<span class="label-text font-semibold text-slate-700">{{
										t("model_endpoint")
									}}</span>
								</label>
								<input
									v-model="newLLM.model_endpoint"
									type="text"
									class="input input-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white font-mono text-sm"
									:placeholder="t('endpoint_placeholder')"
									required
								/>
							</div>

							<div class="form-control mt-6">
								<label class="label">
									<span class="label-text font-semibold text-slate-700">{{
										t("model_key")
									}}</span>
								</label>
								<input
									v-model="newLLM.model_key"
									type="password"
									class="input input-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white"
									required
								/>
							</div>
						</div>

						<!-- 高级选项 -->
						<div
							class="bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl border border-slate-200 overflow-hidden"
						>
							<div class="collapse collapse-arrow">
								<input type="checkbox" v-model="showAdvancedOptions" />
								<div
									class="collapse-title font-bold text-lg text-slate-800 bg-white/50"
								>
									<div class="flex items-center">
										<div
											class="w-2 h-6 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full mr-3"
										></div>
										{{ t("advanced_options") }}
									</div>
								</div>
								<div class="collapse-content bg-white/30 p-6">
									<div class="space-y-6">
										<div class="form-control">
											<label class="label">
												<span
													class="label-text font-semibold text-slate-700"
													>{{ t("context_limit") }}</span
												>
											</label>
											<input
												v-model.number="contentLimitInK"
												type="number"
												class="input input-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white"
												min="1"
												step="1"
												required
											/>
											<label class="label">
												<span class="label-text-alt text-slate-500">
													{{ t("context_limit_description") }}
												</span>
											</label>
										</div>

										<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
											<div class="form-control">
												<label
													class="label cursor-pointer bg-white/60 rounded-lg p-3 border border-slate-200 hover:bg-white/80 transition-colors"
												>
													<span
														class="label-text font-semibold text-slate-700"
														>{{ t("support_function_call") }}</span
													>
													<input
														v-model="newLLM.support_function_call"
														type="checkbox"
														class="checkbox checkbox-primary"
													/>
												</label>
											</div>

											<div class="form-control">
												<label
													class="label cursor-pointer bg-white/60 rounded-lg p-3 border border-slate-200 hover:bg-white/80 transition-colors"
												>
													<span
														class="label-text font-semibold text-slate-700"
														>{{ t("support_json_output") }}</span
													>
													<input
														v-model="newLLM.support_json_output"
														type="checkbox"
														class="checkbox checkbox-primary"
													/>
												</label>
											</div>

											<div class="form-control">
												<label
													class="label cursor-pointer bg-white/60 rounded-lg p-3 border border-slate-200 hover:bg-white/80 transition-colors"
												>
													<span
														class="label-text font-semibold text-slate-700"
														>{{ t("support_vision") }}</span
													>
													<input
														v-model="newLLM.support_vision"
														type="checkbox"
														class="checkbox checkbox-primary"
													/>
												</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- 提交按钮 -->
						<div class="flex justify-end gap-4 pt-4">
							<button
								type="button"
								@click="closeAddDialog"
								class="px-6 py-3 text-slate-600 bg-slate-100 border border-slate-300 rounded-lg hover:bg-slate-200 hover:border-slate-400 transition-all duration-200 font-medium"
								:disabled="loadingState.isAdding"
							>
								{{ t("cancel") }}
							</button>
							<button
								type="submit"
								class="px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 font-medium"
								:disabled="loadingState.isAdding"
							>
								<span v-if="!loadingState.isAdding">{{
									t("add_custom_llm")
								}}</span>
								<span v-else class="loading loading-spinner loading-sm"></span>
							</button>
						</div>
					</form>
				</div>
				<div class="modal-backdrop" @click="closeAddDialog"></div>
			</div>
		</Teleport>

		<!-- 编辑LLM Dialog -->
		<Teleport to="body">
			<div v-if="showEditDialog" class="modal modal-open">
				<div
					class="modal-box max-w-3xl bg-white/95 backdrop-blur-sm border border-slate-200 shadow-2xl"
				>
					<div class="flex justify-between items-center mb-8">
						<div>
							<h3
								class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
							>
								{{ t("edit_custom_llm") }}
							</h3>
							<p class="text-slate-600 mt-1">
								{{ t("modify_llm_configuration") }}
							</p>
						</div>
						<button
							@click="closeEditDialog"
							class="btn btn-sm btn-circle btn-ghost hover:bg-slate-100"
						>
							<XMarkIcon class="w-5 h-5" />
						</button>
					</div>

					<form @submit.prevent="saveEdit" class="space-y-8" v-if="editLLM">
						<!-- 基本信息 -->
						<div
							class="bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl p-6 border border-slate-200"
						>
							<h4
								class="font-bold mb-6 text-xl text-slate-800 flex items-center"
							>
								<div
									class="w-2 h-6 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full mr-3"
								></div>
								{{ t("basic_info") }}
							</h4>

							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div class="form-control">
									<label class="label">
										<span
											class="label-text font-semibold text-slate-700"
											>{{ t("model_name") }}</span
										>
									</label>
									<input
										v-model="editLLM.model_name"
										type="text"
										class="input input-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white"
										required
									/>
								</div>

								<div class="form-control">
									<label class="label">
										<span
											class="label-text font-semibold text-slate-700"
											>{{ t("model_protocol") }}</span
										>
									</label>
									<select
										v-model="editLLM.model_type"
										class="select select-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white"
										required
									>
										<option
											v-for="protocol in modelProtocols"
											:key="protocol.value"
											:value="protocol.value"
										>
											{{ protocol.label }}
										</option>
									</select>
								</div>
							</div>

							<div class="form-control mt-6">
								<label class="label">
									<span class="label-text font-semibold text-slate-700">{{
										t("model_endpoint")
									}}</span>
								</label>
								<input
									v-model="editLLM.model_endpoint"
									type="text"
									class="input input-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white font-mono text-sm"
									required
								/>
							</div>

							<div class="form-control mt-6">
								<label class="label">
									<span class="label-text font-semibold text-slate-700">{{
										t("model_key")
									}}</span>
								</label>
								<input
									v-model="editLLM.model_key"
									type="password"
									class="input input-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white"
									required
								/>
							</div>
						</div>

						<!-- 高级选项 -->
						<div
							class="bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl border border-slate-200 overflow-hidden"
						>
							<div class="collapse collapse-arrow">
								<input type="checkbox" v-model="showEditAdvancedOptions" />
								<div
									class="collapse-title font-bold text-lg text-slate-800 bg-white/50"
								>
									<div class="flex items-center">
										<div
											class="w-2 h-6 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full mr-3"
										></div>
										{{ t("advanced_options") }}
									</div>
								</div>
								<div class="collapse-content bg-white/30 p-6">
									<div class="space-y-6">
										<div class="form-control">
											<label class="label">
												<span
													class="label-text font-semibold text-slate-700"
													>{{ t("context_limit") }}</span
												>
											</label>
											<input
												v-model.number="editContentLimitInK"
												type="number"
												class="input input-bordered border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-white"
												min="1"
												step="1"
												required
											/>
											<label class="label">
												<span class="label-text-alt text-slate-500">
													{{ t("context_limit_description") }}
												</span>
											</label>
										</div>

										<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
											<div class="form-control">
												<label
													class="label cursor-pointer bg-white/60 rounded-lg p-3 border border-slate-200 hover:bg-white/80 transition-colors"
												>
													<span
														class="label-text font-semibold text-slate-700"
														>{{ t("support_function_call") }}</span
													>
													<input
														v-model="editLLM.support_function_call"
														type="checkbox"
														class="checkbox checkbox-primary"
													/>
												</label>
											</div>

											<div class="form-control">
												<label
													class="label cursor-pointer bg-white/60 rounded-lg p-3 border border-slate-200 hover:bg-white/80 transition-colors"
												>
													<span
														class="label-text font-semibold text-slate-700"
														>{{ t("support_json_output") }}</span
													>
													<input
														v-model="editLLM.support_json_output"
														type="checkbox"
														class="checkbox checkbox-primary"
													/>
												</label>
											</div>

											<div class="form-control">
												<label
													class="label cursor-pointer bg-white/60 rounded-lg p-3 border border-slate-200 hover:bg-white/80 transition-colors"
												>
													<span
														class="label-text font-semibold text-slate-700"
														>{{ t("support_vision") }}</span
													>
													<input
														v-model="editLLM.support_vision"
														type="checkbox"
														class="checkbox checkbox-primary"
													/>
												</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- 提交按钮 -->
						<div class="flex justify-end gap-4 pt-4">
							<button
								type="button"
								@click="closeEditDialog"
								class="px-6 py-3 text-slate-600 bg-slate-100 border border-slate-300 rounded-lg hover:bg-slate-200 hover:border-slate-400 transition-all duration-200 font-medium"
								:disabled="loadingState.isSaving"
							>
								{{ t("cancel") }}
							</button>
							<button
								type="submit"
								class="px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 font-medium"
								:disabled="loadingState.isSaving"
							>
								<span v-if="!loadingState.isSaving">{{ t("save") }}</span>
								<span v-else class="loading loading-spinner loading-sm"></span>
							</button>
						</div>
					</form>
				</div>
				<div class="modal-backdrop" @click="closeEditDialog"></div>
			</div>
		</Teleport>

		<!-- 删除确认Dialog -->
		<DeleteConfirmDialog
			:show="showDeleteDialog"
			:item-name="deleteTarget?.model_name"
			:is-loading="loadingState.isDeleting"
			@close="closeDeleteDialog"
			@confirm="confirmDeleteLLM"
		/>
	</div>
</template>
