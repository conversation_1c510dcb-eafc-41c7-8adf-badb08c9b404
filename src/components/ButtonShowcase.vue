<template>
  <div class="p-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-4xl font-bold text-slate-800 mb-8 text-center">
        全局按钮样式展示
      </h1>
      
      <!-- 渐变按钮 -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold text-slate-700 mb-6">渐变按钮</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">主要按钮</h3>
            <div class="space-y-3">
              <button class="btn-gradient-primary w-full gap-2">
                <PlusIcon class="w-4 h-4" />
                创建项目
              </button>
              <button class="btn-gradient-primary btn-sm w-full gap-2">
                <PlusIcon class="w-3 h-3" />
                小尺寸
              </button>
              <button class="btn-gradient-primary btn-lg w-full gap-2">
                <PlusIcon class="w-5 h-5" />
                大尺寸
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">成功按钮</h3>
            <div class="space-y-3">
              <button class="btn-gradient-success w-full gap-2">
                <CheckIcon class="w-4 h-4" />
                保存成功
              </button>
              <button class="btn-gradient-success btn-sm w-full gap-2">
                <CheckIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">警告按钮</h3>
            <div class="space-y-3">
              <button class="btn-gradient-warning w-full gap-2">
                <ExclamationTriangleIcon class="w-4 h-4" />
                警告操作
              </button>
              <button class="btn-gradient-warning btn-sm w-full gap-2">
                <ExclamationTriangleIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">危险按钮</h3>
            <div class="space-y-3">
              <button class="btn-gradient-danger w-full gap-2">
                <TrashIcon class="w-4 h-4" />
                删除项目
              </button>
              <button class="btn-gradient-danger btn-sm w-full gap-2">
                <TrashIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">紫色按钮</h3>
            <div class="space-y-3">
              <button class="btn-gradient-purple w-full gap-2">
                <SparklesIcon class="w-4 h-4" />
                特殊功能
              </button>
              <button class="btn-gradient-purple btn-sm w-full gap-2">
                <SparklesIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 轮廓按钮 -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold text-slate-700 mb-6">轮廓按钮</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">主要轮廓</h3>
            <div class="space-y-3">
              <button class="btn-outline-primary w-full gap-2">
                <EyeIcon class="w-4 h-4" />
                查看详情
              </button>
              <button class="btn-outline-primary btn-sm w-full gap-2">
                <EyeIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">成功轮廓</h3>
            <div class="space-y-3">
              <button class="btn-outline-success w-full gap-2">
                <CheckCircleIcon class="w-4 h-4" />
                确认操作
              </button>
              <button class="btn-outline-success btn-sm w-full gap-2">
                <CheckCircleIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">警告轮廓</h3>
            <div class="space-y-3">
              <button class="btn-outline-warning w-full gap-2">
                <ExclamationCircleIcon class="w-4 h-4" />
                注意事项
              </button>
              <button class="btn-outline-warning btn-sm w-full gap-2">
                <ExclamationCircleIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">危险轮廓</h3>
            <div class="space-y-3">
              <button class="btn-outline-danger w-full gap-2">
                <XCircleIcon class="w-4 h-4" />
                取消操作
              </button>
              <button class="btn-outline-danger btn-sm w-full gap-2">
                <XCircleIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 特殊按钮 -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold text-slate-700 mb-6">特殊按钮</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">现代化按钮</h3>
            <div class="space-y-3">
              <button class="btn-modern w-full gap-2">
                <CogIcon class="w-4 h-4" />
                设置选项
              </button>
              <button class="btn-modern btn-sm w-full gap-2">
                <CogIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">玻璃态按钮</h3>
            <div class="space-y-3">
              <button class="btn-glass w-full gap-2">
                <StarIcon class="w-4 h-4" />
                玻璃效果
              </button>
              <button class="btn-glass btn-sm w-full gap-2">
                <StarIcon class="w-3 h-3" />
                小尺寸
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 状态演示 -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold text-slate-700 mb-6">按钮状态</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">禁用状态</h3>
            <div class="space-y-3">
              <button class="btn-gradient-primary w-full" disabled>
                禁用的主要按钮
              </button>
              <button class="btn-outline-primary w-full" disabled>
                禁用的轮廓按钮
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">加载状态</h3>
            <div class="space-y-3">
              <button class="btn-gradient-primary w-full gap-2">
                <span class="loading loading-spinner loading-sm"></span>
                加载中...
              </button>
              <button class="btn-outline-primary w-full gap-2">
                <span class="loading loading-spinner loading-sm"></span>
                处理中...
              </button>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200">
            <h3 class="text-lg font-semibold mb-4 text-slate-700">组合使用</h3>
            <div class="space-y-3">
              <div class="flex gap-2">
                <button class="btn-outline-danger btn-sm flex-1">
                  取消
                </button>
                <button class="btn-gradient-primary btn-sm flex-1">
                  确认
                </button>
              </div>
              <div class="flex gap-2">
                <button class="btn-modern btn-sm">
                  <CogIcon class="w-3 h-3" />
                </button>
                <button class="btn-gradient-success btn-sm flex-1">
                  保存设置
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 使用说明 -->
      <section class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-slate-200">
        <h2 class="text-2xl font-bold text-slate-700 mb-6">使用说明</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-lg font-semibold mb-3 text-slate-700">渐变按钮类</h3>
            <ul class="space-y-2 text-sm text-slate-600">
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-gradient-primary</code> - 主要操作</li>
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-gradient-success</code> - 成功操作</li>
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-gradient-warning</code> - 警告操作</li>
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-gradient-danger</code> - 危险操作</li>
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-gradient-purple</code> - 特殊操作</li>
            </ul>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-3 text-slate-700">轮廓按钮类</h3>
            <ul class="space-y-2 text-sm text-slate-600">
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-outline-primary</code> - 次要操作</li>
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-outline-success</code> - 确认操作</li>
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-outline-warning</code> - 注意操作</li>
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-outline-danger</code> - 取消操作</li>
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-modern</code> - 现代化样式</li>
              <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-glass</code> - 玻璃态效果</li>
            </ul>
          </div>
        </div>
        <div class="mt-6">
          <h3 class="text-lg font-semibold mb-3 text-slate-700">尺寸修饰符</h3>
          <p class="text-sm text-slate-600 mb-2">可以与任何按钮类组合使用：</p>
          <ul class="space-y-1 text-sm text-slate-600">
            <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-sm</code> - 小尺寸</li>
            <li>默认尺寸（无需额外类）</li>
            <li><code class="bg-slate-100 px-2 py-1 rounded">.btn-lg</code> - 大尺寸（仅渐变按钮支持）</li>
          </ul>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  PlusIcon,
  CheckIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  SparklesIcon,
  EyeIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  XCircleIcon,
  CogIcon,
  StarIcon
} from '@heroicons/vue/24/outline'
</script>
