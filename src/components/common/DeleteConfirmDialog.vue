<script setup lang="ts">
	import { computed } from "vue";
	import { useI18n } from "vue-i18n";
	import { XMarkIcon, TrashIcon } from "@heroicons/vue/24/outline";

	const { t } = useI18n();

	// Props
	interface Props {
		show: boolean;
		title?: string;
		message?: string;
		itemName?: string;
		isLoading?: boolean;
		dangerText?: string;
	}

	const props = withDefaults(defineProps<Props>(), {
		title: "",
		message: "",
		itemName: "",
		isLoading: false,
		dangerText: "",
	});

	// Emits
	const emit = defineEmits<{
		close: [];
		confirm: [];
	}>();

	// Computed
	const dialogTitle = computed(() => {
		return props.title || t("confirm_delete");
	});

	const dialogMessage = computed(() => {
		if (props.message) {
			return props.message;
		}
		if (props.itemName) {
			return t("confirm_delete_item_message", { name: props.itemName });
		}
		return t("confirm_delete_default_message");
	});

	const warningText = computed(() => {
		return props.dangerText || t("delete_warning_irreversible");
	});

	// Methods
	const handleClose = () => {
		if (!props.isLoading) {
			emit("close");
		}
	};

	const handleConfirm = () => {
		if (!props.isLoading) {
			emit("confirm");
		}
	};

	const handleBackdropClick = () => {
		handleClose();
	};
</script>

<template>
	<Teleport to="body">
		<div v-if="show" class="modal modal-open">
			<div class="modal-box bg-white/95 backdrop-blur-sm border border-red-200 shadow-2xl">
				<div class="flex justify-between items-center mb-6">
					<div class="flex items-center">
						<div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
							<TrashIcon class="w-6 h-6 text-red-500" />
						</div>
						<h3 class="text-2xl font-bold text-red-600">{{ dialogTitle }}</h3>
					</div>
					<button
						@click="handleClose"
						class="btn btn-sm btn-circle btn-ghost hover:bg-red-100"
						:disabled="isLoading"
					>
						<XMarkIcon class="w-5 h-5" />
					</button>
				</div>

				<div class="py-6">
					<p class="text-slate-700 mb-6 text-lg">
						{{ dialogMessage }}
					</p>
					<div class="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl p-4">
						<div class="flex items-start">
							<svg 
								xmlns="http://www.w3.org/2000/svg" 
								class="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" 
								fill="none" 
								viewBox="0 0 24 24" 
								stroke="currentColor"
							>
								<path 
									stroke-linecap="round" 
									stroke-linejoin="round" 
									stroke-width="2" 
									d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" 
								/>
							</svg>
							<p class="text-red-700 text-sm font-medium">
								{{ warningText }}
							</p>
						</div>
					</div>
				</div>

				<div class="flex justify-end gap-4 pt-4">
					<button
						@click="handleClose"
						class="px-6 py-3 text-slate-600 bg-slate-100 border border-slate-300 rounded-lg hover:bg-slate-200 hover:border-slate-400 transition-all duration-200 font-medium"
						:disabled="isLoading"
					>
						{{ t("cancel") }}
					</button>
					<button
						@click="handleConfirm"
						class="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 font-medium"
						:disabled="isLoading"
					>
						<span v-if="!isLoading">{{ t("delete") }}</span>
						<span v-else class="loading loading-spinner loading-sm"></span>
					</button>
				</div>
			</div>
			<div class="modal-backdrop" @click="handleBackdropClick"></div>
		</div>
	</Teleport>
</template>
