<template>
	<div>
		<div ref="dialog" class="modal" role="dialog" :class="{ 'modal-open': model }">
			<div
				class="modal-box relative md:max-w-[80%] md:w-[80%] lg:max-w-[60%] lg:w-[60%] max-w-full w-full"
			>
				<h3 class="font-bold text-xl">{{ t("runtime") }}</h3>
				<form method="dialog" @submit="model = false">
					<button class="btn btn-sm btn-circle btn-ghost fixed right-2 top-2">
						✕
					</button>
				</form>
				<component_wait v-if="model">
					<runtimeEngine :data="prop.data" v-if="model"></runtimeEngine>
				</component_wait>
				<div class="modal-action">
					<form method="dialog" @submit="model = false">
						<button class="btn">{{ t("close") }}</button>
					</form>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import component_wait from "@/components/utils/component_wait.vue";
	import { ref, defineAsyncComponent } from "vue";
	import { useI18n } from "vue-i18n";
	const prop = defineProps(["data"]);
	const model = defineModel();
	const dialog = ref<HTMLDialogElement>();
	const { t } = useI18n();
	const runtimeEngine = defineAsyncComponent(
		() => import("@/components/utils/runtime_engine.vue")
	);
</script>
