<script setup lang="ts">
	import { defineProps } from "vue";
	import component_wait from "@/components/utils/component_wait.vue";
	import viewer from "@/components/viewer.vue";
	import logInput from "@/components/utils/log_input_view.vue";
	import { Error, Success } from "@/utils/notify";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	const props = defineProps<{
		log: {
			input: string;
			output?: string;
			error?: string;
			total_time?: number;
		};
		loading?: boolean;
	}>();

	const copyOutput = () => {
		if (props.log.output) {
			navigator.clipboard.writeText(props.log.output);
			Success(t("copy_success"), t("copied_to_clipboard"));
		}
	};
</script>

<template>
	<div>
		<div v-if="props.loading" class="text-center py-4">
			<span class="loading loading-spinner loading-md"></span>
		</div>
		<div v-else>
			<div class="mb-4">
				<h3 class="text-sm font-semibold mb-2">{{ t("input") }}：</h3>
				<div>
					<component_wait>
						<logInput :logInput="props.log.input"></logInput>
					</component_wait>
				</div>
			</div>
			<div v-if="props.log.output">
				<div class="flex justify-between items-center mb-2" :title="t('copy_output')">
					<h3 class="text-sm font-semibold">{{ t("output") }}：</h3>
					<button
						@click="copyOutput"
						class="p-1 hover:bg-gray-100 rounded-md text-gray-500 hover:text-gray-700 transition-colors"
						:title="t('copy_output')"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-4 w-4"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
							/>
						</svg>
					</button>
				</div>
				<div>
					<component_wait>
						<viewer :output="props.log.output"></viewer>
					</component_wait>
				</div>
			</div>
			<div v-if="props.log.error">
				<h3 class="text-sm font-semibold mb-2">{{ t("error") }}：</h3>
				<div class="bg-red-50 p-3 rounded-md border border-red-200">
					<pre class="text-red-600 text-sm whitespace-pre-wrap break-words">{{
						props.log.error
					}}</pre>
				</div>
			</div>
			<p class="text-sm text-gray-500 mt-2">
				{{ t("runtime") }}：{{ props.log.total_time }}{{ t("seconds") }}
			</p>
		</div>
	</div>
</template>
