<script setup lang="ts">
	import { ref, defineProps, onMounted, onUnmounted } from "vue";
	import { defineAsyncComponent } from "vue";
	const GlobalSearch = defineAsyncComponent(() => import("@/components/search.vue"));
	const Header = defineAsyncComponent(() => import("@/components/dashboard/header.vue"));
	const Menu = defineAsyncComponent(() => import("@/components/dashboard/menu.vue"));
	const props = defineProps({
		current: {
			type: String,
		},
	});

	const isSearchOpen = ref(false);
	const appHeight = ref(window.innerHeight);

	const setAppHeight = () => {
		appHeight.value = window.innerHeight;
		document.documentElement.style.setProperty("--app-height", `${appHeight.value}px`);
	};

	onMounted(() => {
		window.addEventListener("keydown", (e) => {
			if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
				e.preventDefault();
				isSearchOpen.value = true;
			}
		});

		window.addEventListener("resize", setAppHeight);
		setAppHeight();
	});

	onUnmounted(() => {
		window.removeEventListener("resize", setAppHeight);
	});
</script>

<template>
	<div
		class="h-screen font-sans text-neutral overflow-x-hidden"
		style="height: var(--app-height)"
	>
		<Header></Header>
		<main
			class="flex flex-col sm:flex-row h-[calc(var(--app-height)-49px)] max-sm:flex-col-reverse"
		>
			<Menu :current="props.current" class="w-full sm:w-auto"></Menu>
			<section
				class="flex-1 w-full h-[calc(var(--app-height)-50px)] overflow-scroll bg-base-100 relative shadow-md"
			>
				<slot></slot>
			</section>
		</main>
		<GlobalSearch :isOpen="isSearchOpen" @close="isSearchOpen = false" />
	</div>
</template>

<style scoped>
	.user-menu-enter-active,
	.user-menu-leave-active {
		transition: opacity 0.3s, transform 0.3s;
	}

	.user-menu-enter-from,
	.user-menu-leave-to {
		opacity: 0;
		transform: translateY(10px);
	}
</style>
