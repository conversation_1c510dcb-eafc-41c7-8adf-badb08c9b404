<template>
	<BaseNode>
		<template #body="{ data }">
			<p class="w-full flex items-center">
				<component
					v-if="iconComponent"
					:is="iconComponent"
					class="w-5 h-5 align-middle text-white bg-neutral rounded-md p-1 box-border shrink-0"
				/>
				<span class="ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
					data.name
				}}</span>
			</p>
			<p
				class="w-full m-0 mt-1 text-xs font-thin whitespace-nowrap text-ellipsis overflow-x-hidden"
				v-for="(v, k) in data.input.params"
			>
				{{ k }} : {{ v.value }}
			</p>
		</template>
	</BaseNode>
</template>
<script setup lang="ts">
	import BaseNode from "@/components/utils/base_node.vue";
	import { useNode } from "@vue-flow/core";
	import { Nodes } from "./nodes.ts";
	const iconComponent = Nodes["code_runner"].icon || null;

	const node = useNode();
	const data = node.node.data;
</script>
<style scoped></style>
