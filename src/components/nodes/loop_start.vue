<script setup lang="ts">
	import { <PERSON><PERSON>, Position } from "@vue-flow/core";
	import { ArrowPathIcon } from "@heroicons/vue/24/solid";
	import Add from "@/components/utils/add.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const emit = defineEmits(["showOptions"]);
	const showOptions = (id: any, data: any, e: MouseEvent) => {
		emit("showOptions", id, data, e);
	};

	defineProps({
		id: {
			type: String,
			required: true,
		},
	});
</script>

<template>
	<div class="myinput relative hover:bg-gray-200">
		<div class="w-full flex items-center">
			<ArrowPathIcon
				class="w-5 h-5 text-white bg-primary rounded-md p-1 box-border shrink-0"
			/>
			<span class="align-middle ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
				t("start_loop_once")
			}}</span>
		</div>
		<div class="mt-1">
			<p class="m-0 text-xs font-thin text-ellipsis overflow-hidden whitespace-nowrap">
				{{ t("each_element_will_execute_once") }}
			</p>
		</div>
		<Add @showOptions="showOptions"></Add>
	</div>
</template>

<style scoped>
	.myinput {
		font-weight: bold;
		display: flex;
		flex-direction: column;
		padding: 10px;
		font-size: 13px;
		border-radius: 15px;
		width: 200px;
		background-color: #fff;
		box-shadow: 1px 4px 7px 0px rgba(0, 0, 0, 0.1);
		transition: 0.3s;
		border: 1px solid #f8f8f8;
	}

	.myinput:hover {
		background-color: #f8f8f8;
		@apply shadow-xl;
	}
</style>
