<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>
		<p class="mt-4">{{ t("json_data_source") }}:</p>
		<select
			class="select select-bordered w-full"
			:placeholder="t('pls-select-json-data-source')"
			v-model="nodeData.data.input.json_data"
		>
			<option disabled selected value="">--{{ t("pls-select") }}--</option>
			<option v-for="i in vars" :value="'$' + i.node + '.' + i.name">
				${{ i.node + "." + i.name }}
			</option>
		</select>
		<p class="mt-4">{{ t("json_path") }}:</p>
		<input
			type="text"
			:placeholder="t('json_path')"
			v-model="nodeData.data.input.params"
			class="input input-bordered w-full"
		/>
		<p class="mt-2 mb-3 text-xs font-thin">
			{{ t("for_example") }}: <span class="bg-base-200 px-1 py-0.5 rounded">a.b.c</span>
			{{ t("or_the_first_element_of_the_array") }}:
			<span class="bg-base-200 px-1 py-0.5 rounded">a.b.0.d</span>
		</p>
		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNode, useNodesData } from "@vue-flow/core";
	import { ref, watch } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let vars = ref(getLinkedVar(prop.id));
</script>
<style scoped></style>
