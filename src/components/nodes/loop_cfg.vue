<script setup lang="ts">
	import { ref, watch, computed } from "vue";
	import { useVueFlow } from "@vue-flow/core";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	const props = defineProps<{
		id: string;
		flowId: string;
	}>();

	const { getNode, updateNode } = useVueFlow(props.flowId);

	const node = computed(() => getNode.value(props.id));
	if (!node.value) throw new Error("Node not found");

	const name = ref(node.value.data?.name || t("loop"));
	const vars = ref(getLinkedVar(props.id));

	// 监听名称变化
	watch(name, (newVal) => {
		updateNode(props.id, {
			data: {
				...node.value?.data,
				name: newVal,
			},
		});
	});

	const loopVar = ref(node.value.data?.input?.loop_var || "");
	const loopSleep = ref(node.value.data?.input?.loop_sleep || 0);
	// 监听循环变量变化
	watch([loopVar, loopSleep], (newVal) => {
		updateNode(props.id, {
			data: {
				...node.value?.data,
				input: {
					...node.value?.data?.input,
					loop_var: newVal[0],
					loop_sleep: newVal[1],
				},
			},
		});
	});

	// 获取输出数据
	const outputs = computed(() => {
		return node.value?.data?.output || [];
	});
</script>

<template>
	<div class="">
		<div class="form-control w-full">
			<label class="label">
				<span class="label-text">{{ t("node_name") }}</span>
			</label>
			<input
				type="text"
				v-model="name"
				:placeholder="t('please_input_node_name')"
				class="input input-bordered w-full"
			/>
		</div>

		<div class="form-control w-full mt-4">
			<label class="label">
				<span class="label-text">{{ t("loop_var") }}</span>
			</label>
			<select
				class="select select-bordered w-full"
				:placeholder="t('please_select_loop_var')"
				v-model="loopVar"
			>
				<option disabled selected value="">--{{ t("pls-select") }}--</option>
				<option v-for="i in vars" :value="'$' + i.node + '.' + i.name">
					{{ i.node + "." + i.name }}
				</option>
			</select>
			<p class="text-xs text-gray-500 mt-2" v-html="t('loop_var_desc')"></p>
		</div>

		<div class="form-control w-full mt-4">
			<label class="label">
				<span class="label-text">{{ t("loop_sleep") }}</span>
			</label>
			<input type="number" v-model="loopSleep" class="input input-bordered w-full" />
			<p class="text-xs text-gray-500 mt-2">
				{{ t("loop_sleep_desc") }}
			</p>
		</div>

		<!-- 添加输出显示部分 -->
		<div class="mt-8">
			<div class="divider"></div>
			<p class="font-medium mb-2">{{ t("output") }}</p>
			<div
				v-for="output in outputs"
				:key="output.name"
				class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
			>
				<span class="font-bold block">${{ name }}.{{ output.name }}</span>
				<p class="m-0 text-xs font-thin">{{ output.desc }}</p>
			</div>
			<div v-if="outputs.length === 0" class="text-gray-400 text-sm text-center py-4">
				{{ t("no_output") }}
			</div>
		</div>
	</div>
</template>

<style scoped>
	.divider {
		@apply my-4 h-px bg-gray-200;
	}
</style>
