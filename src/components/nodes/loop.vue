<script setup lang="ts">
	import { Handle, Position, useVueFlow, useNode, useNodesData } from "@vue-flow/core";
	import { computed, onMounted, ref } from "vue";
	import { ArrowPathIcon } from "@heroicons/vue/24/solid";
	import Workflow from "@/components/workflow.vue";
	import Vue3Iframe from "@/utils/vue3iframe/index.vue";
	import Add from "@/components/utils/add.vue";
	import { useNodeOptions } from "@/composables/useNodeOptions";
	import { initializeNodeData } from "@/utils/workflow_utils";
	import { GetNode } from "@/components/nodes/nodes.ts";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const props = defineProps({
		id: {
			type: String,
			required: true,
		},
	});

	const emit = defineEmits(["show-config", "showOptions"]);
	const node = useNode(props.id);
	const nodeData = useNodesData(props.id);
	const workflow = ref({});

	const nodeName = computed(() => {
		return nodeData.value?.data?.name || t("loop");
	});

	// 获取循环变量
	const loopVar = computed(() => {
		return nodeData.value?.data?.input?.loop_var || "";
	});

	// 在 script setup 部分的开头添加尺寸常量
	const DEFAULT_SIZE = {
		width: 600,
		height: 400,
		minWidth: 600,
		minHeight: 400,
	};

	// 添加临时大小状态用于拖拽过程
	const tempSize = ref({
		width: DEFAULT_SIZE.width,
		height: DEFAULT_SIZE.height,
	});

	// 实际节点大小
	const nodeSize = ref({
		width: DEFAULT_SIZE.width,
		height: DEFAULT_SIZE.height,
	});

	// 添加一个状态来控制是否显示遮罩
	const isResizing = ref(false);

	// 处理调整大小
	const handleResize = (e: MouseEvent) => {
		e.stopPropagation();

		// 开始调整大小显示遮罩
		isResizing.value = true;

		const startX = e.pageX;
		const startY = e.pageY;
		const startWidth = nodeSize.value.width;
		const startHeight = nodeSize.value.height;

		// 初始化临时大小
		tempSize.value = {
			width: startWidth,
			height: startHeight,
		};

		const handleMouseMove = (moveEvent: MouseEvent) => {
			moveEvent.preventDefault();
			moveEvent.stopPropagation();

			// 计算鼠标移动的距离（可以是正值或负值）
			const deltaX = moveEvent.pageX - startX;
			const deltaY = moveEvent.pageY - startY;

			// 计算新的尺寸，同时确保不小于最小值
			const newWidth = startWidth + deltaX;
			const newHeight = startHeight + deltaY;

			// 更新临时大小，限制最小尺寸
			tempSize.value = {
				width: Math.max(DEFAULT_SIZE.minWidth, newWidth),
				height: Math.max(DEFAULT_SIZE.minHeight, newHeight),
			};
		};

		const handleMouseUp = (upEvent: MouseEvent) => {
			upEvent.preventDefault();
			upEvent.stopPropagation();

			// 鼠标释放时更新实际大小
			nodeSize.value = {
				width: tempSize.value.width,
				height: tempSize.value.height,
			};

			// 结束调整大小时移除遮罩
			isResizing.value = false;

			window.removeEventListener("mousemove", handleMouseMove);
			window.removeEventListener("mouseup", handleMouseUp);
			document.body.style.cursor = "";
		};

		document.body.style.cursor = "se-resize";
		window.addEventListener("mousemove", handleMouseMove);
		window.addEventListener("mouseup", handleMouseUp);
	};

	// 创建初始工作流
	const createInitialWorkflow = () => {
		let loopStart = GetNode("loop_start", t);
		let loopEnd = GetNode("loop_end", t);
		return {
			nodes: [
				{
					id: `${props.id}-start`,
					type: "loop_start",
					position: { x: 100, y: 100 },
					data: initializeNodeData(loopStart, loopStart.label, 0),
					deletable: false,
				},
				{
					id: `${props.id}-end`,
					type: "loop_end",
					position: { x: 500, y: 100 },
					data: initializeNodeData(loopEnd, loopEnd.label, 0),
					deletable: false,
				},
			],
			edges: [
				{
					id: `${props.id}-start-end-edge`,
					source: `${props.id}-start`,
					target: `${props.id}-end`,
					type: "custom",
				},
			],
		};
	};

	const workflowLoad = () => {
		if (!node) return createInitialWorkflow();
		let workflow = nodeData.value?.data?.input?.workflow;
		if (workflow?.nodes?.length > 0) {
			const hasStart = workflow?.nodes?.some((node: any) => node.type === "loop_start");
			const hasEnd = workflow?.nodes?.some((node: any) => node.type === "loop_end");

			if (hasStart && hasEnd) {
				return nodeData.value?.data?.input?.workflow;
			}
		}
		return createInitialWorkflow();
	};
	workflow.value = workflowLoad();

	// 生成子工作流的唯一ID
	const subFlowId = `sub-flow-${props.id}`;

	// 处理子工作流的变化
	const handleWorkflowChange = (e: any) => {
		const { edges, nodes } = useVueFlow(subFlowId);

		nodeData.value!.data.input.workflow = {
			nodes: nodes.value,
			edges: edges.value,
		};
	};

	// 处理子工作流中节点的配置
	const handleNodeConfig = (nodeId: string, nodeType: string) => {
		emit("show-config", nodeId, nodeType, subFlowId);
	};

	const showOptions = (id: any, data: any, e: MouseEvent) => {
		emit("showOptions", id, data, e);
	};
	onMounted(() => {});

	// 只获取循环相关节点
	const { options: loopNodeOptions } = useNodeOptions(
		(type) =>
			type !== "loop_start" &&
			type !== "loop_end" &&
			type !== "in" &&
			type !== "out" &&
			type !== "loop"
	);
</script>

<template>
	<div
		class="rounded-xl border shadow-xl bg-white relative"
		:style="{
			width: `${nodeSize.width}px`,
			height: `${nodeSize.height}px`,
			minWidth: `${DEFAULT_SIZE.minWidth}px`,
			minHeight: `${DEFAULT_SIZE.minHeight}px`,
		}"
		:class="{ 'pointer-events-none': node.node.selected }"
	>
		<Handle type="target" :position="Position.Left" />
		<div class="p-3 h-full flex flex-col">
			<div class="flex items-center gap-2 mb-2">
				<ArrowPathIcon class="w-5 h-5 text-gray-600" />
				<div class="flex-1 overflow-hidden">
					<div class="w-full flex items-center justify-between m-0">
						<span class="text-sm text-ellipsis overflow-hidden whitespace-nowrap">
							{{ nodeName }}
						</span>
						<span v-if="loopVar" class="text-xs font-thin text-gray-500 ml-2">
							{{ t("loop_var") }}: {{ loopVar }}
						</span>
					</div>
				</div>
			</div>

			<!-- 子工作流区域 -->
			<div class="border rounded-lg flex-1 overflow-hidden pointer-events-auto relative">
				<!-- 添加遮罩层 -->
				<div v-if="isResizing" class="workflow-mask"></div>

				<Vue3Iframe :inheritStyles="true" :height="`${nodeSize.height - 50}px`">
					<Workflow
						:flowId="subFlowId"
						:data="workflow"
						:showMiniMap="false"
						:showControls="true"
						:options="loopNodeOptions || []"
						@change="handleWorkflowChange"
						@node-config="handleNodeConfig"
					/>
				</Vue3Iframe>
			</div>

			<!-- 添加调整大小的手柄 -->
			<div class="resize-handle" @mousedown.prevent="handleResize"></div>

			<!-- 添加一个拖拽时的预览遮罩 -->
			<div
				v-if="tempSize.width !== nodeSize.width || tempSize.height !== nodeSize.height"
				class="resize-preview"
				:style="{
					width: `${tempSize.width}px`,
					height: `${tempSize.height}px`,
				}"
			></div>
		</div>
		<Add @showOptions="showOptions"></Add>
	</div>
</template>

<style scoped>
	.pointer-events-auto {
		pointer-events: auto !important;
	}

	/* 确保子工作流容器正确定位 */
	:deep(.vue-flow) {
		position: absolute !important;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
	}

	.resize-handle {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 15px;
		height: 15px;
		cursor: se-resize;
		background: linear-gradient(135deg, transparent 50%, #666 50%);
		border-bottom-right-radius: 4px;
	}

	.resize-preview {
		position: absolute;
		top: 0;
		left: 0;
		border: 2px dashed #666;
		pointer-events: none;
		z-index: 1000;
		background: transparent;
	}

	.workflow-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.8);
		z-index: 1000;
		cursor: se-resize;
	}
</style>
