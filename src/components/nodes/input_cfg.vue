<template>
	<div v-if="nodeData" class="rounded-lg">
		<div class="mb-4">
			<label class="block text-md font-medium mb-2">{{ t("node_name") }}</label>
			<NodeName :nodeId="prop.id" v-model="nodeData.data.name" class="w-full" />
		</div>

		<div class="mb-4">
			<h3 class="text-md font-medium mb-2">{{ t("input_variable") }}</h3>
			<div
				v-if="!nodeData.data.output || nodeData.data.output.length == 0"
				class="bg-gray-100 rounded-lg p-4 text-center text-gray-500 text-sm"
			>
				- {{ t("add_input_variable") }} -
			</div>
			<draggable
				v-else
				v-model="nodeData.data.output"
				item-key="id"
				class="space-y-3"
				@start="onDragStart"
				@end="onDragEnd"
				handle=".drag-handle"
			>
				<template #item="{ element: item, index }">
					<div class="bg-white p-3 rounded-lg shadow-md pl-0">
						<div class="flex items-start">
							<div
								class="drag-handle ml-2 mr-2 cursor-move text-gray-400 hover:text-blue-600 transition-colors duration-200 flex-shrink-0 self-start"
								:title="t('drag_to_reorder')"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-7 align-middle"
									fill="none"
									viewBox="0 0 8 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="3"
										d="M4 6 h0 M4 12 h0 M4 18 h0"
									/>
								</svg>
							</div>
							<div class="flex-grow self-center">
								<div class="flex items-center space-x-2 self-center mb-2">
									<select
										v-model="item.type"
										@change="handleTypeChange(item)"
										class="select select-sm select-bordered flex-grow-[1] w-4"
									>
										<option value="text">{{ t("text") }}</option>
										<option value="longtext">{{ t("longtext") }}</option>
										<option value="select">{{ t("select") }}</option>
										<option value="radio">{{ t("radio") }}</option>
										<option value="checkbox">{{ t("checkbox") }}</option>
										<option value="image">{{ t("image") }}</option>
									</select>
									<input
										v-model="item.name"
										:placeholder="t('name')"
										class="input input-sm input-bordered flex-grow-[4] w-10"
									/>
									<button
										@click="inDelProp(item)"
										class="btn btn-xs btn-outline btn-error"
									>
										{{ t("delete") }}
									</button>
								</div>
								<!-- 选项配置部分 -->
								<div
									v-if="
										item.type === 'select' ||
										item.type === 'radio' ||
										item.type === 'checkbox'
									"
									class="mt-2 p-3 bg-gray-50 rounded-md"
								>
									<div class="flex items-center mb-2 space-x-3">
										<h4 class="font-sm">{{ t("options") }}:</h4>
										<button
											@click="addOption(item)"
											class="btn btn-primary btn-xs btn-outline"
										>
											+ {{ t("add_option") }}
										</button>
									</div>
									<div
										v-for="(option, optionIndex) in item.options"
										:key="optionIndex"
										class="flex items-center space-x-2 mb-2"
									>
										<input
											v-model="option.value"
											:placeholder="t('option_value')"
											class="input input-xs input-bordered flex-grow flex-shrink-1 w-full min-w-20"
										/>
										<button
											@click="toggleDefault(item, option.value)"
											class="btn btn-xs"
											:class="{
												'btn-primary': isDefaultOption(
													item,
													option.value
												),
												'btn-outline': !isDefaultOption(
													item,
													option.value
												),
											}"
										>
											{{ t("default") }}
										</button>
										<button
											@click="removeOption(item, optionIndex)"
											class="btn btn-xs btn-outline btn-error"
										>
											{{ t("delete") }}
										</button>
									</div>
								</div>
								<!-- 图片类型的配置 -->
								<div v-if="item.type === 'image'" class="mt-2">
									<div class="flex items-center space-x-2">
										<label class="text-sm"
											>{{ t("allow_upload_image_number") }}:</label
										>
										<input
											v-model.number="item.default"
											type="number"
											min="1"
											:max="10"
											class="input input-sm input-bordered w-24"
											:placeholder="t('default_1_image')"
										/>
									</div>
								</div>
								<!-- 文本和长文本类型的默认值输入 -->
								<div v-else class="mt-2">
									<input
										v-if="item.type === 'text'"
										v-model="item.default"
										:placeholder="t('default_value')"
										class="input input-sm input-bordered w-full"
									/>
									<textarea
										v-else-if="item.type === 'longtext'"
										v-model="item.default"
										:placeholder="t('default_value')"
										class="textarea textarea-bordered w-full"
										rows="3"
									></textarea>
								</div>
							</div>
						</div>
					</div>
				</template>
			</draggable>
		</div>

		<button @click="inAddProp" class="btn btn-primary w-full mt-2">
			+ {{ t("add_new_variable") }}
		</button>
	</div>
</template>

<script setup lang="ts">
	import { useNodesData, useVueFlow } from "@vue-flow/core";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { Error } from "@/utils/notify";
	import draggable from "vuedraggable";
	import { useI18n } from "vue-i18n";
	import { ref, watch, nextTick } from "vue";
	const drag = ref(false);
	const { t } = useI18n();
	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);

	let inAddProp = () => {
		let output = nodeData.value?.data.output || [];
		output.push({ type: "text", name: t("input") + (output.length + 1), default: "" });
		if (nodeData.value) {
			nodeData.value.data.output = output;
		}
	};

	let inDelProp = (i: any) => {
		let output = nodeData.value?.data.output || [];
		output.splice(output.indexOf(i), 1);
		if (nodeData.value) {
			nodeData.value.data.output = output;
		}
	};

	const handleTypeChange = (item: any) => {
		if (
			(item.type === "select" || item.type === "radio" || item.type === "checkbox") &&
			(!Array.isArray(item.options) || item.options.length === 0)
		) {
			item.options = [{ value: t("options") + "1" }];
		}
		// 重置默认值
		if (item.type === "checkbox") {
			item.default = [];
		} else if (item.type === "image") {
			item.default = 1;
		} else {
			item.default = "";
		}
	};

	const toggleDefault = (item: any, value: string) => {
		if (item.type === "checkbox") {
			if (!Array.isArray(item.default)) {
				item.default = [];
			}
			const index = item.default.indexOf(value);
			if (index > -1) {
				item.default.splice(index, 1);
			} else {
				item.default.push(value);
			}
		} else {
			item.default = item.default === value ? "" : value;
		}
	};

	const isDefaultOption = (item: any, value: string) => {
		if (item.type === "checkbox") {
			return Array.isArray(item.default) && item.default.includes(value);
		} else {
			return item.default === value;
		}
	};

	const addOption = (item: any) => {
		if (!item.options) {
			item.options = [];
		}
		item.options.push({ value: t("options") + (item.options.length + 1) });
	};

	const removeOption = (item: any, index: number) => {
		if (item.options.length > 1) {
			const removedValue = item.options[index].value;
			item.options.splice(index, 1);
			// 如果删除的是默认选项，重置默认值
			if (item.type === "select" || item.type === "radio") {
				if (item.default === removedValue) {
					item.default = "";
				}
			} else if (item.type === "checkbox") {
				item.default = item.default.filter((v: string) => v !== removedValue);
			}
		} else {
			Error(t("error"), t("at_least_one_option"));
		}
	};

	// 监听选项变化，更新默认值
	watch(
		() => nodeData.value?.data.output,
		(newOutput) => {
			if (newOutput) {
				nextTick(() => {
					newOutput.forEach((item: any) => {
						if (!Array.isArray(item.options)) {
							item.options = [];
						}
						if (item.type === "select" || item.type === "radio") {
							if (
								!item.options.some(
									(option: any) => option.value === item.default
								)
							) {
								item.default = "";
							}
						}
					});
				});
			}
		},
		{ deep: true }
	);

	// 拖动相关函数保持不变
	const onDragStart = (evt: any) => {
		drag.value = true;
		if (evt.item) {
			evt.item.classList.add("opacity-75", "scale-102");
		}
	};

	const onDragEnd = (evt: any) => {
		drag.value = false;
		if (evt.item) {
			evt.item.classList.remove("opacity-75", "scale-102");
		}
	};
</script>

<style scoped>
	.drag-handle {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 4px;
		border-radius: 4px;
		background-color: #f3f4f6;
	}

	.drag-handle:hover {
		background-color: #e5e7eb;
	}

	@media (max-width: 768px) {
		.drag-handle svg {
			width: 20px;
			height: 20px;
		}
	}

	.scale-102 {
		transform: scale(1.02);
	}
</style>
