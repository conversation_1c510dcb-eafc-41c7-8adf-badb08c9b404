<template>
	<div v-if="nodeData">
		<p class="text-ellipsis overflow-hidden text-sm">
			{{ nodeInput.plugin.desc }}
		</p>
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<div v-for="(i, index) in nodeInput.plugin.config" :key="index">
			<div v-if="i.type == 'editor'">
				<p class="mt-4">{{ i.label }}：</p>
				<EditorWithFullscreen
					v-model="nodeInput.plugin.config[index].default"
					:vars="vars"
					editor-type="code"
				/>
			</div>

			<div v-if="i.type == 'text'">
				<p class="mt-4">{{ i.label }}：</p>
				<input
					v-model="nodeInput.plugin.config[index].default"
					type="text"
					class="input input-bordered w-full"
				/>
			</div>

			<div v-if="i.type == 'select'">
				<p class="mt-4">{{ i.label }}：</p>
				<select
					class="select select-bordered w-full"
					v-model="nodeInput.plugin.config[index].default"
				>
					<option v-for="j in i.options" :value="j">{{ j }}</option>
				</select>
			</div>
			<div v-if="i.type == 'note'">
				<p class="mt-4">{{ i.label }}：</p>
				<p class="text-sm bg-slate-50 rounded-lg p-3">{{ i.default }}</p>
			</div>
		</div>
		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import EditorWithFullscreen from "@/components/utils/editor_with_fullscreen.vue";
	import { useNode, useNodesData } from "@vue-flow/core";
	import { ref, watch } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	interface ConfigItem {
		type: string;
		label: string;
		default: any;
		options?: string[];
	}

	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let vars = ref(getLinkedVar(prop.id));
	let nodeInput = ref({
		plugin: {
			desc: "",
			config: [] as ConfigItem[],
		},
	});
	if (nodeData.value && nodeData.value.data && nodeData.value.data.input) {
		nodeInput = nodeData.value.data.input;
	}
</script>
<style scoped></style>
