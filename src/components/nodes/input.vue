<template>
	<div class="myinput relative hover:bg-gray-200">
		<div class="w-full flex items-center">
			<component
				v-if="iconComponent"
				:is="iconComponent"
				class="w-5 h-5 align-middle text-white bg-primary rounded-md p-1 box-border shrink-0"
			/>
			<span class="align-middle ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
				data.name
			}}</span>
		</div>
		<div class="mt-1">
			<p
				v-for="i in data.output"
				class="m-0 text-xs font-thin text-ellipsis overflow-hidden whitespace-nowrap"
			>
				{{ t("type") }}:{{ typeTranslate(i.type) }},{{ t("name") }}:{{ i.name }}
			</p>
		</div>
		<Add @showOptions="showOptions"></Add>
	</div>
</template>
<script setup lang="ts">
	import Add from "@/components/utils/add.vue";
	import { useNode } from "@vue-flow/core";
	import { useI18n } from "vue-i18n";
	import { Nodes } from "./nodes.ts";
	const iconComponent = Nodes["in"].icon || null;
	const { t } = useI18n();

	const emit = defineEmits(["showOptions"]);
	const showOptions = (id: any, data: any, e: MouseEvent) => {
		emit("showOptions", id, data, e);
	};

	const node = useNode();
	const data = node.node.data;

	const typeTranslate = (type: string) => {
		switch (type) {
			case "text":
				return t("text");
			case "longtext":
				return t("longtext");
			case "select":
				return t("select");
			case "radio":
				return t("radio");
			case "checkbox":
				return t("checkbox");
			case "image":
				return t("image");
			default:
				return type;
		}
	};
</script>
<style scoped>
	.myinput {
		font-weight: bold;
		display: flex;
		flex-direction: column;
		padding: 10px;
		font-size: 13px;
		border-radius: 15px;
		width: 200px;
		/* max-width: 300px; */
		background-color: #fff;
		box-shadow: 1px 4px 7px 0px rgba(0, 0, 0, 0.1);
		transition: 0.3s;
		border: 1px solid #f8f8f8;
	}
	.myinput:hover {
		background-color: #f8f8f8;
		@apply shadow-xl;
	}
</style>
