<script setup lang="ts">
	import { BaseEdge, EdgeLabel<PERSON><PERSON><PERSON>, getBezierPath, useVueFlow } from "@vue-flow/core";
	import { computed, onMounted } from "vue";

	const props = defineProps({
		id: {
			type: String,
			required: true,
		},
		sourceX: {
			type: Number,
			required: true,
		},
		sourceY: {
			type: Number,
			required: true,
		},
		targetX: {
			type: Number,
			required: true,
		},
		targetY: {
			type: Number,
			required: true,
		},
		sourcePosition: {
			type: String,
			required: true,
		},
		targetPosition: {
			type: String,
			required: true,
		},
		markerEnd: {
			type: String,
			required: false,
		},
		style: {
			type: Object,
			required: false,
		},
	});

	// @ts-ignore
	const path = computed(() => getBezierPath(props));
	const { findEdge } = useVueFlow();
	let sourceNodeID = findEdge(props.id)?.source;
	let targetNodeID = findEdge(props.id)?.target;
	onMounted(() => {
		// 当鼠标移入时，显示+按钮，由于vueflow不支持，只能用这种方式
		let el = document.querySelector(`.vue-flow__edges>g[data-id="${props.id}"]`);
		if (el) {
			// add hover event
			el.addEventListener("mouseenter", () => {
				document
					?.querySelector(`.addBtn[data-id="${props.id}"]`)
					?.classList.add("opacity-100");
			});
			el.addEventListener("mouseleave", () => {
				document
					?.querySelector(`.addBtn[data-id="${props.id}"]`)
					?.classList.remove("opacity-100");
			});
		}
	});

	const add = (e: MouseEvent) => {
		sourceNodeID = findEdge(props.id)?.source;
		targetNodeID = findEdge(props.id)?.target;
		emit(
			"showOptions",
			sourceNodeID,
			{
				flowai_insertIntoMiddle: true,
				flowai_target: targetNodeID,
				flowai_delete_edge: props.id,
			},
			e
		);
	};

	const emit = defineEmits(["showOptions"]);
</script>

<script lang="ts">
	export default {
		inheritAttrs: false,
	};
</script>

<template>
	<BaseEdge
		class="mycustomEdge"
		:id="id"
		:style="style"
		:path="path[0]"
		:marker-end="markerEnd"
	/>
	<EdgeLabelRenderer>
		<div
			:data-id="id"
			:style="{
				pointerEvents: 'all',
				transform: `translate(-50%, -50%) translate(${path[1]}px,${path[2]}px)`,
			}"
			class="nodrag nopan addBtn opacity-0 hover:opacity-100 box-border transition absolute bg-primary text-white w-5 h-5 rounded-full pointer text-center flex justify-center items-center"
		>
			<button
				class="p-0 m-0 w-full h-full flex justify-center items-center"
				@click="(e) => add(e)"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 16 16"
					class="w-3 h-3"
					stroke="currentColor"
					stroke-width="2"
					fill="none"
				>
					<path d="M8 3v10M3 8h10" />
				</svg>
			</button>
		</div>
	</EdgeLabelRenderer>
</template>

<style scoped></style>
