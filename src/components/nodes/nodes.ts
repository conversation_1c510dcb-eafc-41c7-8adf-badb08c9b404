import { defineAsyncComponent, type Component } from "vue";
import {
	HomeIcon,
	DocumentMagnifyingGlassIcon,
	ChatBubbleLeftEllipsisIcon,
	CubeTransparentIcon,
	DocumentIcon,
	GlobeAltIcon,
	ClockIcon,
	WrenchScrewdriverIcon,
	CommandLineIcon,
	CodeBracketIcon,
	ArrowRightEndOnRectangleIcon,
	CogIcon,
	ArrowPathIcon,
} from "@heroicons/vue/24/solid";

type input = {
	name: string;
	type: string;
	default: any;
	injectVar?: boolean;
};

type output = {
	name: string;
	type: string;
	desc: string;
};

export type NodeType = {
	show: boolean;
	type: string;
	label: string; // for variable,no space
	nodeShowName: string; // for show
	icon: Component | null;
	node: Component | null;
	cfgRender: Component | null;
	config?: input[];
	output: output[];
	group: string;
	helpDoc?: string;
};

export type NodesType = {
	[key: string]: NodeType;
};

export const Nodes = {
	in: {
		show: true,
		type: "in",
		label: "$nodes.in.label",
		nodeShowName: "$nodes.in.nodeShowName",
		icon: HomeIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/input.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/input_cfg.vue")),
		output: [],
		group: "$nodes.group.base",
		helpDoc: "$nodes.in.helpDoc",
	},
	out: {
		show: true,
		type: "out",
		label: "$nodes.out.label",
		nodeShowName: "$nodes.out.nodeShowName",
		icon: ArrowRightEndOnRectangleIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/output.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/output_cfg.vue")),
		config: [{ name: "from", type: "string", default: "", injectVar: true }],
		output: [],
		group: "$nodes.group.base",
		helpDoc: "$nodes.out.helpDoc",
	},
	browser: {
		show: true,
		type: "browser",
		label: "$nodes.browser.label",
		nodeShowName: "$nodes.browser.nodeShowName",
		icon: DocumentMagnifyingGlassIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/browser.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/browser_cfg.vue")),
		config: [{ name: "url", type: "string", default: "", injectVar: true }],
		output: [{ name: "body", type: "string", desc: "$nodes.browser.body" }],
		group: "$nodes.group.data_acquisition",
		helpDoc: "$nodes.browser.helpDoc",
	},
	llm: {
		show: true,
		type: "llm",
		label: "$nodes.llm.label",
		nodeShowName: "$nodes.llm.nodeShowName",
		icon: ChatBubbleLeftEllipsisIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/llm.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/llm_cfg.vue")),
		config: [
			{ name: "model", type: "string", default: "gpt-4.1-nano" },
			{ name: "system", type: "string", default: "" },
			{ name: "prompt", type: "string", default: "" },
			{ name: "max_tokens", type: "number", default: 0 },
		],
		output: [{ name: "result", type: "string", desc: "$nodes.llm.llm_output_desc" }],
		group: "$nodes.group.ai_model",
		helpDoc: "$nodes.llm.helpDoc",
	},
	llm_cls: {
		show: true,
		type: "llm_cls",
		label: "$nodes.llm_cls.label",
		nodeShowName: "$nodes.llm_cls.nodeShowName",
		icon: CubeTransparentIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/llm_classifier.vue")),
		cfgRender: defineAsyncComponent(
			() => import("@/components/nodes/llm_classifier_cfg.vue")
		),
		config: [
			{ name: "condition_content", type: "string", default: "", injectVar: true },
			{ name: "model", type: "string", default: "gpt-4.1-nano" },
			{ name: "class", type: "object", default: [] },
			{ name: "prompt", type: "string", default: "" },
		],
		output: [],
		group: "$nodes.group.ai_model",
		helpDoc: "$nodes.llm_cls.helpDoc",
	},
	llm_classifier_class: {
		show: false,
		type: "llm_classifier_class",
		label: "$nodes.llm_classifier_class.label",
		nodeShowName: "$nodes.llm_classifier_class.nodeShowName",
		icon: null,
		node: defineAsyncComponent(
			() => import("@/components/nodes/llm_classifier_class.vue")
		),
		cfgRender: null,
		config: [],
		output: [],
		group: "$nodes.group.ai_model",
	},
	template: {
		show: true,
		type: "template",
		label: "$nodes.template.label",
		nodeShowName: "$nodes.template.nodeShowName",
		icon: DocumentIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/template.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/template_cfg.vue")),
		config: [{ name: "template", type: "string", default: "", injectVar: true }],
		output: [
			{ name: "template", type: "string", desc: "$nodes.template.template_output_desc" },
		],
		group: "$nodes.group.data_processing",
		helpDoc: "$nodes.template.helpDoc",
	},
	adv_template: {
		show: true,
		type: "adv_template",
		label: "$nodes.adv_template.label",
		nodeShowName: "$nodes.adv_template.nodeShowName",
		icon: DocumentIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/adv_template.vue")),
		cfgRender: defineAsyncComponent(
			() => import("@/components/nodes/adv_template_cfg.vue")
		),
		config: [{ name: "template", type: "string", default: "", injectVar: false }],
		output: [
			{
				name: "template",
				type: "string",
				desc: "$nodes.adv_template.template_output_desc",
			},
		],
		group: "$nodes.group.data_processing",
		helpDoc: "$nodes.adv_template.helpDoc",
	},
	http: {
		show: true,
		type: "http",
		label: "$nodes.http.label",
		nodeShowName: "$nodes.http.nodeShowName",
		icon: GlobeAltIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/http.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/http_cfg.vue")),
		config: [
			{ name: "url", type: "string", default: "", injectVar: true },
			{ name: "method", type: "string", default: "GET" },
			{
				name: "headers",
				type: "array",
				default: [],
			},
			{ name: "body", type: "string", default: "" },
		],
		output: [
			{ name: "body", type: "string", desc: "$nodes.http.output_body_desc" },
			{ name: "status", type: "string", desc: "$nodes.http.output_status_desc" },
		],
		group: "$nodes.group.data_acquisition",
		helpDoc: "$nodes.http.helpDoc",
	},
	current_time: {
		show: true,
		type: "current_time",
		label: "$nodes.current_time.label",
		nodeShowName: "$nodes.current_time.nodeShowName",
		icon: ClockIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/time.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/time_cfg.vue")),
		config: [],
		output: [
			{
				name: "time",
				type: "string",
				desc: "$nodes.current_time.output_desc",
			},
		],
		group: "$nodes.group.tool",
		helpDoc: "$nodes.current_time.helpDoc",
	},
	plugin: {
		// 这个是所有插件的父插件，必须存在，且必须设置为false
		show: false,
		type: "plugin",
		label: "$nodes.plugin.label",
		nodeShowName: "$nodes.plugin.nodeShowName",
		icon: WrenchScrewdriverIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/plugin.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/plugin_cfg.vue")),
		config: [
			{ name: "plugin_name", type: "string", default: "" },
			{ name: "plugin", type: "object", default: {} },
		],
		output: [],
		group: "$nodes.group.tool",
		helpDoc: "$nodes.plugin.helpDoc",
	},
	code_runner: {
		show: true,
		type: "code_runner",
		label: "$nodes.code_runner.label",
		nodeShowName: "$nodes.code_runner.nodeShowName",
		icon: CommandLineIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/code_runner.vue")),
		cfgRender: defineAsyncComponent(
			() => import("@/components/nodes/code_runner_cfg.vue")
		),
		config: [
			{ name: "code", type: "string", default: "" },
			{ name: "params", type: "object", default: {} },
			{ name: "requirements", type: "array", default: [] },
		],
		output: [{ name: "output", type: "object", desc: "$nodes.code_runner.output_desc" }],
		group: "$nodes.group.tool",
		helpDoc: "$nodes.code_runner.helpDoc",
	},
	json_param_extract: {
		show: true,
		type: "json_param_extract",
		label: "$nodes.json_param_extract.label",
		nodeShowName: "$nodes.json_param_extract.nodeShowName",
		icon: CodeBracketIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/json_param_extract.vue")),
		cfgRender: defineAsyncComponent(
			() => import("@/components/nodes/json_param_extract_cfg.vue")
		),
		config: [
			{
				name: "json_from",
				type: "string",
				default: "var",
			},
			{
				name: "json_data",
				type: "string",
				default: "{}",
				injectVar: true,
			},
			{
				name: "params",
				type: "string",
				default: "",
			},
		],
		output: [
			{ name: "output", type: "object", desc: "$nodes.json_param_extract.output_desc" },
		],
		group: "$nodes.group.data_processing",
		helpDoc: "$nodes.json_param_extract.helpDoc",
	},
	condition: {
		show: true,
		type: "condition",
		label: "$nodes.condition.label",
		nodeShowName: "$nodes.condition.nodeShowName",
		icon: CogIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/condition.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/condition_cfg.vue")),
		config: [
			{ name: "condition_content", type: "string", default: "", injectVar: true },
			{ name: "options", type: "array", default: [] },
		],
		output: [],
		group: "$nodes.group.base",
		helpDoc: "$nodes.condition.helpDoc",
	},
	condition_class: {
		show: false,
		type: "condition_class",
		label: "$nodes.condition_class.label",
		nodeShowName: "$nodes.condition_class.nodeShowName",
		icon: null,
		node: defineAsyncComponent(() => import("@/components/nodes/condition_class.vue")),
		cfgRender: null,
		config: [],
		output: [
			{ name: "result", type: "string", desc: "$nodes.condition_class.output_desc" },
		],
		group: "$nodes.group.base",
	},
	loop: {
		show: true,
		type: "loop",
		label: "$nodes.loop.label",
		nodeShowName: "$nodes.loop.nodeShowName",
		icon: ArrowPathIcon,
		node: defineAsyncComponent(() => import("./loop.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/loop_cfg.vue")),
		config: [
			{ name: "name", type: "string", default: "" },
			{ name: "loop_var", type: "string", default: "", injectVar: true },
			{ name: "loop_sleep", type: "number", default: 0 },
			{ name: "workflow", type: "object", default: { nodes: [], edges: [] } },
		],
		output: [{ name: "result", type: "string", desc: "$nodes.loop.output_desc" }],
		group: "$nodes.group.base",
		helpDoc: "$nodes.loop.helpDoc",
	},
	loop_start: {
		show: false,
		type: "loop_start",
		label: "$nodes.loop_start.label",
		nodeShowName: "$nodes.loop_start.nodeShowName",
		icon: null,
		node: defineAsyncComponent(() => import("./loop_start.vue")),
		cfgRender: null,
		config: [],
		output: [
			{ name: "item", type: "string", desc: "$nodes.loop_start.loop_start_item_desc" },
			{ name: "index", type: "number", desc: "$nodes.loop_start.loop_start_index_desc" },
		],
		group: "$nodes.group.base",
		helpDoc: "$nodes.loop_start.helpDoc",
	},
	loop_end: {
		show: false,
		type: "loop_end",
		label: "$nodes.loop_end.label",
		nodeShowName: "$nodes.loop_end.nodeShowName",
		icon: null,
		node: defineAsyncComponent(() => import("./loop_end.vue")),
		cfgRender: defineAsyncComponent(() => import("./loop_end_cfg.vue")),
		config: [
			{
				name: "export_var",
				type: "string",
				default: "$nodes.loop_end.loop_end_export_var_desc",
				injectVar: true,
			},
			{ name: "loop_sleep", type: "number", default: 0 },
		],
		output: [],
		group: "$nodes.group.base",
		helpDoc: "$nodes.loop_end.helpDoc",
	},
	json_pretty: {
		show: true,
		type: "json_pretty",
		label: "$nodes.json_pretty.label",
		nodeShowName: "$nodes.json_pretty.nodeShowName",
		icon: CodeBracketIcon,
		node: defineAsyncComponent(() => import("./json_pretty.vue")),
		cfgRender: defineAsyncComponent(() => import("./json_pretty_cfg.vue")),
		config: [{ name: "json_data", type: "string", default: "", injectVar: true }],
		output: [{ name: "output", type: "string", desc: "$nodes.json_pretty.output_desc" }],
		group: "$nodes.group.data_processing",
		helpDoc: "$nodes.json_pretty.helpDoc",
	},
	llm_agent: {
		show: true,
		type: "llm_agent",
		label: "$nodes.llm_agent.label",
		nodeShowName: "$nodes.llm_agent.nodeShowName",
		icon: WrenchScrewdriverIcon,
		node: defineAsyncComponent(() => import("./llm_agent.vue")),
		cfgRender: defineAsyncComponent(() => import("./llm_agent_cfg.vue")),
		config: [
			{ name: "prompt", type: "string", default: "", injectVar: true },
			{ name: "output_lang", type: "string", default: "zh" },
			{ name: "model", type: "string", default: "gpt-4.1-nano" },
			{ name: "tools", type: "array", default: [] },
			{ name: "max_iterations", type: "number", default: 5 },
			{ name: "mcp_servers", type: "array", default: [] },
		],
		output: [{ name: "result", type: "string", desc: "$nodes.llm_agent.output_desc" }],
		group: "$nodes.group.ai_model",
		helpDoc: "$nodes.llm_agent.helpDoc",
	},
} as NodesType;

export const nodeI18nVarTranslate = (value: string, t: any) => {
	if (typeof value === "string" && value.startsWith("$nodes.")) {
		return t(value.split("$")[1]);
	}
	return value;
};

export const GetNode = (nodeType: string, t: any) => {
	const node = Nodes[nodeType];
	if (t) {
		node.label = nodeI18nVarTranslate(node.label, t);
		node.group = nodeI18nVarTranslate(node.group, t);
		node.nodeShowName = nodeI18nVarTranslate(node.nodeShowName, t);
		if (node.helpDoc) {
			node.helpDoc = nodeI18nVarTranslate(node.helpDoc, t);
		}
		if (node.config) {
			node.config.forEach((item) => {
				item.default = nodeI18nVarTranslate(item.default, t);
			});
		}
		if (node.output) {
			node.output.forEach((item) => {
				item.desc = nodeI18nVarTranslate(item.desc, t);
			});
		}
	}
	return node;
};
