<template>
	<BaseNode>
		<template #body="{ data }">
			<p class="w-full flex items-center">
				<component
					v-if="iconComponent"
					:is="iconComponent"
					class="w-5 h-5 align-middle text-white bg-blue-500 rounded-md p-1 box-border shrink-0"
				/>
				<span class="ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
					data.name
				}}</span>
			</p>
			<p class="m-0 mt-1 text-xs font-thin">{{ t("return_system_time") }}</p>
		</template>
	</BaseNode>
</template>
<script setup lang="ts">
	import BaseNode from "@/components/utils/base_node.vue";
	import { Nodes } from "./nodes.ts";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const iconComponent = Nodes["current_time"].icon || null;
</script>
<style scoped></style>
