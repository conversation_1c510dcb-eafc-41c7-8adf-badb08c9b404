<template>
	<div v-if="nodeData">
		<p class="mt-2">{{ t("set_condition") }}</p>
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>
		<p class="mt-4">{{ t("select_condition_content") }}:</p>
		<select
			class="select select-bordered w-full"
			v-model="nodeData.data.input.condition_content"
		>
			<option disabled selected value="">--{{ t("pls-select") }}--</option>
			<option
				v-for="i in vars"
				:key="i.node + i.name"
				:value="'$' + i.node + '.' + i.name"
			>
				${{ i.node + "." + i.name }}
			</option>
		</select>
		<p class="mt-4">{{ t("condition_options") }}:</p>
		<p
			class="mb-2 bg-slate-50 text-sm rounded-lg py-3 text-center text-gray-500"
			v-if="!nodeData.data.input.options || nodeData.data.input.options.length == 0"
		>
			{{ t("please_add_condition_options") }}
		</p>
		<draggable
			v-model="nodeData.data.input.options"
			item-key="id"
			handle=".drag-handle"
			@start="onDragStart"
			@end="onDragEnd"
		>
			<template #item="{ element: option, index }">
				<div class="mt-2 first-of-type:mt-0 bg-gray-50 rounded-lg p-2">
					<div class="flex items-start">
						<div
							class="drag-handle ml-2 mr-2 cursor-move text-gray-400 hover:text-blue-600 transition-colors duration-200 flex-shrink-0 self-start"
							:title="t('drag_to_reorder')"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								class="h-5 align-middle"
								fill="none"
								viewBox="0 0 8 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="3"
									d="M4 6 h0 M4 12 h0 M4 18 h0"
								/>
							</svg>
						</div>
						<div class="flex-grow self-center">
							<condition-options
								v-model="nodeData.data.input.options[index]"
								@delete="deleteOption(index)"
							/>
						</div>
					</div>
				</div>
			</template>
		</draggable>
		<button class="btn btn-primary mt-3 w-full" @click="addOption">
			+ {{ t("add_condition") }}
		</button>
	</div>
</template>

<script setup lang="ts">
	import { useNodesData } from "@vue-flow/core";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import { ref, watch } from "vue";
	import NodeName from "@/components/utils/node_name_input.vue";
	import ConditionOptions from "@/components/utils/condition_options.vue";
	import { ConditionType } from "@/utils/condition_types";
	import draggable from "vuedraggable";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	const prop = defineProps(["id"]);
	const nodeData = useNodesData(prop.id);
	const vars = ref(getLinkedVar(prop.id));
	const drag = ref(false);

	const addOption = () => {
		let allOptions = nodeData.value?.data.input.options || [];
		const newOption = {
			id: `condition_class_${Math.random().toString(36).slice(-8)}`,
			condition_type: ConditionType.EQUALS,
			value: t("content") + (allOptions.length + 1),
		};
		allOptions.push(newOption);
		if (nodeData.value) {
			nodeData.value.data.input.options = allOptions;
		}
	};

	const deleteOption = (index: number) => {
		if (nodeData.value) {
			const newOptions = [...nodeData.value.data.input.options];
			newOptions.splice(index, 1);
			nodeData.value.data.input.options = newOptions;
		}
	};

	const onDragStart = (evt: any) => {
		drag.value = true;
		if (evt.item) {
			evt.item.classList.add("opacity-75", "scale-102");
		}
	};

	const onDragEnd = (evt: any) => {
		drag.value = false;
		if (evt.item) {
			evt.item.classList.remove("opacity-75", "scale-102");
		}
	};

	watch(
		() => nodeData.value?.data.input.options,
		() => {},
		{ deep: true }
	);
</script>

<style scoped>
	.drag-handle {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 4px;
		border-radius: 4px;
		background-color: #f3f4f6;
	}

	.drag-handle:hover {
		background-color: #e5e7eb;
	}

	.scale-102 {
		transform: scale(1.02);
	}

	@media (max-width: 768px) {
		.drag-handle svg {
			width: 20px;
			height: 20px;
		}
	}
</style>
