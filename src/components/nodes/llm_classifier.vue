<template>
	<div class="myinput relative hover:bg-gray-200 w-[450px]">
		<p class="w-full flex items-center">
			<component
				v-if="iconComponent"
				:is="iconComponent"
				class="w-5 h-5 align-middle text-white bg-accent rounded-md p-1 box-border shrink-0"
			/>
			<span class="ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
				data.name
			}}</span>
		</p>
		<p class="m-0 mt-1 text-xs font-thin">{{ t("model") }}: {{ data.input.model }}</p>
		<p
			class="m-0 text-xs font-thin w-full h-[50px]"
			v-for="(i, index) in data.input.class"
		></p>

		<Handle type="target" :position="Position.Left" />
		<!-- <Add @showOptions="showOptions"></Add> -->
	</div>
</template>

<script setup lang="ts">
	import { useNodeId, useVueFlow } from "@vue-flow/core";
	import { watch } from "vue";
	import { Handle, Position, useNode } from "@vue-flow/core";
	import { Nodes } from "./nodes.ts";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const iconComponent = Nodes["llm_cls"].icon || null;

	const emit = defineEmits(["showOptions"]);
	const showOptions = (id: any, data: any, e: MouseEvent) => {
		emit("showOptions", id, data, e);
	};

	const node = useNode();
	const data = node.node.data;

	let { addNodes, updateNode, findNode, nodes, removeNodes } = useVueFlow();
	let currentNodeID = useNodeId();
	// 对比已有的节点，如果val有没有在已有的节点中的话，就添加，如果val有在已有的节点中的话，就删除
	watch(data.input.class, (val) => {
		// 找到val中有,currentChild中没有的节点，添加它
		let currentChild = nodes.value
			.filter((i) => i.parentNode == currentNodeID)
			.map((e) => e.id);
		let valNotInCurrentChild = val.filter((i: any) => !currentChild.includes(i.id));
		valNotInCurrentChild.forEach((i: any) => {
			addNodes({
				id: i.id,
				type: "llm_classifier_class",
				label: val.name,
				extent: "parent",
				position: { x: 10, y: 110 + (currentChild.length - 1) * 50 },
				parentNode: currentNodeID,
				selectable: false,
				// deletable: false,
				draggable: false,
				focusable: false,
				targetPosition: Position.Right,
				data: {
					name: i.name,
					input: { class: i.name },
				},
			});
		});

		// 找到val没有，但是currentChild中有的节点，删除它
		currentChild = nodes.value
			.filter((i) => i.parentNode == currentNodeID)
			.map((e) => e.id);
		let valInCurrentChild = currentChild.filter(
			(i: any) => !val.map((i: any) => i.id).includes(i)
		);
		valInCurrentChild.forEach((i: any) => {
			removeNodes(i);
		});

		// 如果val和currentChild中都有的话，就更新
		val.forEach((i: any) => {
			let node = findNode(i.id);
			if (node) {
				node.data.input.class = i.name;
				node.label = i.name;
			}
		});
	});
</script>
<style scoped>
	.myinput {
		font-weight: bold;
		display: flex;
		padding: 10px;
		font-size: 13px;
		border-radius: 15px;
		width: 200px;
		min-height: 60px;
		min-width: 300px;
		max-width: 600px;
		background-color: #fff;
		box-shadow: 1px 4px 7px 0px rgba(0, 0, 0, 0.1);
		transition: 0.3s;
		border: 1px solid #f8f8f8;
		flex-direction: column;
		justify-content: start;
		align-items: start;
	}
	.myinput:hover {
		background-color: #f8f8f8;
		@apply shadow-xl;
	}
</style>
