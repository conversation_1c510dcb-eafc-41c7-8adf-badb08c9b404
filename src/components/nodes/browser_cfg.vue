<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<p class="mt-4">{{ t("browser_url") }}:</p>
		<p class="mb-2 align-middle">
			<input
				id="url_from_var"
				type="radio"
				name="url_from"
				class="radio radio-primary align-middle radio-xs"
				v-model="url_from"
				value="var"
			/>
			<label for="url_from_var" class="align-middle ml-1">{{
				t("from_variable")
			}}</label>

			<input
				id="url_from_input"
				type="radio"
				name="url_from"
				v-model="url_from"
				value="input"
				class="ml-3 radio radio-primary align-middle radio-xs"
			/><label for="url_from_input" class="align-middle ml-1">{{
				t("direct_input")
			}}</label>
		</p>
		<input
			v-if="url_from == 'input'"
			type="text"
			:placeholder="t('input_url')"
			v-model="nodeData.data.input.url"
			class="input input-bordered w-full"
		/>

		<select
			v-if="url_from == 'var'"
			class="select select-bordered w-full"
			v-model="nodeData.data.input.url"
		>
			<option disabled selected value="">--{{ t("pls-select") }}--</option>
			<option v-for="i in vars" :value="'$' + i.node + '.' + i.name">
				${{ i.node + "." + i.name }}
			</option>
		</select>

		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNode, useNodesData } from "@vue-flow/core";
	import { ref, watch } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let url_from = ref("var");
	if (nodeData.value && nodeData.value.data && nodeData.value.data.input) {
		let url = nodeData.value.data.input.url || "";
		if (url.startsWith("http")) {
			url_from.value = "input";
		}
	}

	watch(url_from, () => {
		if (nodeData.value) {
			nodeData.value.data.input.url = "";
		}
	});

	if (nodeData.value && !nodeData.value.data.input.url) {
		nodeData.value.data.input.url = "";
	}
	let vars = ref(getLinkedVar(prop.id));
</script>
<style scoped></style>
