<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<p class="mt-4">{{ t("select_output_content") }}:</p>
		<select class="select select-bordered w-full" v-model="nodeData.data.input.from">
			<option disabled selected value="">--{{ t("pls-select") }}--</option>
			<option v-for="i in vars" :value="'$' + i.node + '.' + i.name">
				${{ i.node + "." + i.name }}
			</option>
		</select>
	</div>
</template>

<script setup lang="ts">
	import { useNode, useNodesData } from "@vue-flow/core";
	import { ref, watch } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let vars = ref(getLinkedVar(prop.id));
</script>
<style scoped></style>
