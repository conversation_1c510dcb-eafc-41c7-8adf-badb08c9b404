<template>
	<div
		class="relative w-[280px] py-2 px-3 hover:bg-gray-200 transition-all rounded-md border border-solid border-gray-300"
	>
		<div class="text-sm font-bold text-neutral overflow-hidden">
			<span class="inline-block max-w-full truncate align-middle">
				<span class="text-primary">{{ conditionTypeLabel }}:</span>
				<span class="ml-1 font-normal text-gray-700">{{ data.input.value }}</span>
			</span>
		</div>
		<Add @showOptions="showOptions"></Add>
	</div>
</template>

<script setup lang="ts">
	import Add from "@/components/utils/add.vue";
	import { useNode } from "@vue-flow/core";
	import { computed } from "vue";
	import { ConditionType, getConditionTypeLabel } from "@/utils/condition_types";
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();
	const emit = defineEmits(["showOptions"]);
	const showOptions = (id: any, data: any, e: MouseEvent) => {
		emit("showOptions", id, data, e);
	};

	const node = useNode();
	const data = node.node.data;

	// 计算属性：根据条件类型获取对应的显示标签
	const conditionTypeLabel = computed(() => {
		return getConditionTypeLabel(data.input.condition_type as ConditionType, t);
	});
</script>

<style scoped>
	.truncate {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
</style>
