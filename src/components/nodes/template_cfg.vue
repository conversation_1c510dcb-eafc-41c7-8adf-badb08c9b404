<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<p class="mt-4">{{ t("content") }}:</p>
		<EditorWithFullscreen
			v-model="nodeData.data.input.template"
			:vars="vars"
			editor-type="code"
		/>
		<p class="mt-0 mb-3 text-xs font-thin" v-html="t('can_use_variables')"></p>
		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNode, useNodesData } from "@vue-flow/core";
	import { ref, watch } from "vue";
	import EditorWithFullscreen from "@/components/utils/editor_with_fullscreen.vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let vars = ref(getLinkedVar(prop.id));
</script>
<style scoped></style>
