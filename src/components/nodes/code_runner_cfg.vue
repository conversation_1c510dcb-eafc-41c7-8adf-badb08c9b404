<template>
	<div v-if="nodeData" class="p-0 space-y-8">
		<div class="space-y-2">
			<h2 class="text-lg font-semibold">{{ t("node_name") }}</h2>
			<NodeName :nodeId="prop.id" v-model="nodeData.data.name" class="w-full" />
		</div>

		<div class="space-y-4">
			<h2 class="text-lg font-semibold flex items-center gap-2 justify-between">
				{{ t("input_params") }}
				<button @click="addInput" class="btn btn-primary btn-sm">
					+ {{ t("add") }}
				</button>
			</h2>
			<div
				v-for="(value, key) in nodeData.data.input.params"
				:key="key"
				class="flex flex-wrap items-center gap-2 pb-2 border-b"
			>
				<input
					v-model="tempKeys[key]"
					@blur="updateKey(key, tempKeys[key])"
					class="input input-bordered input-sm flex-grow"
					:placeholder="t('variable_name')"
				/>
				<select v-model="value.type" class="select select-bordered select-sm">
					<option value="context">{{ t("get_from_context") }}</option>
					<option value="manual">{{ t("manual_input") }}</option>
				</select>
				<input
					v-if="value.type === 'manual'"
					v-model="value.value"
					class="input input-bordered input-sm flex-grow"
					:placeholder="t('input_value')"
				/>
				<select
					v-else
					v-model="value.value"
					class="select select-bordered select-sm flex-grow"
				>
					<option v-for="i in contextVars" :value="'$' + i.node + '.' + i.name">
						${{ i.node + "." + i.name }}
					</option>
				</select>
				<button @click="removeInput(key)" class="btn btn-sm">{{ t("delete") }}</button>
			</div>
		</div>

		<div class="space-y-2">
			<h2 class="text-lg font-semibold">{{ t("js_code") }}</h2>
			<EditorWithFullscreen
				v-model="nodeData.data.input.code"
				:vars="vars"
				editor-type="ext"
				language="javascript"
				editor-class="h-64"
			/>
			<p class="text-xs text-gray-500 m-0" v-html="t('code_runner_desc')"></p>
		</div>

		<!-- <div class="space-y-4">
			<h2 class="text-lg font-semibold">依赖包</h2>
			<div
				v-for="(pkg, index) in nodeData.data.input.requirements"
				:key="index"
				class="flex items-center gap-2 pb-2"
			>
				<input
					v-model="nodeData.data.input.requirements[index]"
					class="input input-bordered input-sm flex-grow"
					placeholder="包名称"
				/>
				<button @click="removeRequirement(index)" class="btn btn-sm">删除</button>
			</div>
			<button @click="addRequirement" class="btn btn-primary btn-sm">添加依赖包</button>
		</div> -->

		<div class="space-y-4">
			<div class="divider"></div>
			<h2 class="text-lg font-semibold">{{ t("output") }}</h2>
			<div
				v-for="i in nodeData.data.output"
				:key="i"
				class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
			>
				<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
				<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
	import { useNode, useNodesData } from "@vue-flow/core";
	import { ref, watch, onMounted } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { Error } from "@/utils/notify";
	import component_wait from "@/components/utils/component_wait.vue";
	import EditorWithFullscreen from "@/components/utils/editor_with_fullscreen.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let contextVars = ref(getLinkedVar(prop.id));
	let vars = ref<{ [key: string]: string }>({});
	const tempKeys = ref<{ [key: string]: string }>({});

	onMounted(() => {
		if (nodeData.value) {
			Object.keys(nodeData.value.data.input.params).forEach((key) => {
				tempKeys.value[key] = key;
			});
		}
	});

	const updateKey = (oldKey: any, newKey: any) => {
		// 判断oldKey，newKey类型是否为string
		if (typeof oldKey !== "string" || typeof newKey !== "string") {
			Error(t("key_must_be_string"));
			return;
		}

		if (nodeData.value && oldKey !== newKey) {
			if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(newKey)) {
				Error(t("invalid_variable_name"));
				tempKeys.value[oldKey] = oldKey; // 恢复原始键名
				return;
			}
			const value = nodeData.value.data.input.params[oldKey];
			delete nodeData.value.data.input.params[oldKey];
			nodeData.value.data.input.params[newKey] = value;
			tempKeys.value[newKey] = newKey;
			delete tempKeys.value[oldKey];
		}
	};

	const addInput = () => {
		if (nodeData.value) {
			const newKey = `input${Object.keys(nodeData.value.data.input.params).length + 1}`;
			nodeData.value.data.input.params[newKey] = { type: "manual", value: "" };
			tempKeys.value[newKey] = newKey;
		}
	};

	const removeInput = (key: any) => {
		if (typeof key !== "string") {
			Error(t("key_must_be_string"));
			return;
		}
		if (nodeData.value) {
			delete nodeData.value.data.input.params[key];
			delete tempKeys.value[key];
		}
	};

	const addRequirement = () => {
		if (nodeData.value) {
			nodeData.value.data.input.requirements.push("");
		}
	};

	const removeRequirement = (index: number) => {
		if (nodeData.value) {
			nodeData.value.data.input.requirements.splice(index, 1);
		}
	};

	watch(
		() => nodeData.value?.data.input.params,
		(newParams) => {
			if (newParams) {
				vars.value = {};
				for (const key in newParams) {
					let var_name = "input." + key;
					vars.value[var_name] = var_name;
				}
			}
		},
		{ deep: true, immediate: true }
	);
</script>
