<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<div class="form-control w-full mt-4">
			<label class="label">
				<span class="label-text">{{ t("export_var") }}</span>
			</label>
			<select
				class="select select-bordered w-full"
				:placeholder="t('please_select_export_var')"
				v-model="nodeData.data.input.export_var"
			>
				<option disabled selected value="">--{{ t("pls-select") }}--</option>
				<option v-for="i in vars" :value="'$' + i.node + '.' + i.name">
					${{ i.node + "." + i.name }}
				</option>
			</select>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNode, useNodesData } from "@vue-flow/core";
	import { ref, watch } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let vars = ref(getLinkedVar(prop.id));

	// 确保input对象存在
	if (nodeData.value && !nodeData.value.data.input) {
		nodeData.value.data.input = {};
	}
</script>

<style scoped>
	.divider {
		@apply my-4 h-px bg-gray-200;
	}
</style>
