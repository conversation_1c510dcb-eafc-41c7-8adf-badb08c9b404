<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<div class="space-y-4 mt-4">
			<h2 class="text-lg font-semibold flex items-center gap-2 justify-between">
				{{ t("input_params") }}
				<button @click="addInput" class="btn btn-primary btn-sm">
					+ {{ t("add") }}
				</button>
			</h2>

			<div
				v-if="Object.keys(nodeData.data.input.params || {}).length === 0"
				class="text-center py-4 bg-slate-50 rounded-lg"
			>
				<div class="max-w-md mx-auto space-y-1">
					<div>
						<h3 class="text-lg font-medium text-gray-700">
							{{ t("no_variable") }}
						</h3>
						<p class="text-sm text-gray-500">
							{{ t("click_add_to_start") }}
						</p>
					</div>
				</div>
			</div>

			<div
				v-else
				v-for="(value, key) in nodeData.data.input.params"
				:key="key"
				class="flex items-center gap-2 p-2 bg-base-100 rounded-lg shadow-sm border border-base-200 hover:border-primary transition-colors duration-200"
			>
				<input
					v-model="tempKeys[key]"
					@blur="updateKey(String(key), tempKeys[key])"
					class="input input-bordered input-sm w-[120px]"
					:placeholder="t('variable_name')"
				/>
				<select
					v-model="value.value"
					class="select select-bordered select-sm flex-1 min-w-[200px]"
				>
					<option disabled selected value="">--{{ t("pls-select") }}--</option>
					<option v-for="i in contextVars" :value="'$' + i.node + '.' + i.name">
						${{ i.node + "." + i.name }}
					</option>
				</select>
				<button
					@click="removeInput(String(key))"
					class="btn btn-sm btn-error btn-outline btn-square"
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="h-4 w-4"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M6 18L18 6M6 6l12 12"
						/>
					</svg>
				</button>
			</div>
		</div>

		<p class="mt-4">{{ t("content") }}:</p>
		<EditorWithFullscreen
			v-model="nodeData.data.input.template"
			:vars="[]"
			editor-type="code"
			:highlightVariables="false"
		/>
		<p class="mt-0 mb-3 text-xs font-thin" v-html="t('can_use_variables')"></p>
		<div class="mt-2 px-4 py-3 bg-slate-50 rounded-lg text-sm">
			<p class="font-medium mb-2">{{ t("go_template_example_title") }}</p>

			<ul class="list-inside space-y-1 text-xs">
				<li>{{ t("variable") }}: <code>&#123;&#123; .varName &#125;&#125;</code></li>
				<li>
					{{ t("condition") }}:
					<code
						>&#123;&#123;if
						.condition&#125;&#125;...&#123;&#123;else&#125;&#125;...&#123;&#123;
						end &#125;&#125;</code
					>
				</li>
				<li>
					{{ t("loop") }}:
					<code
						>&#123;&#123;range .items&#125;&#125;...&#123;&#123; end
						&#125;&#125;</code
					>
				</li>
				<li>
					{{ t("pipe") }}:
					<code>&#123;&#123; .value | upper | lower &#125;&#125;</code>
				</li>
				<li>
					{{ t("function") }}:
					<code>&#123;&#123; json_parse .value &#125;&#125;</code>
				</li>
			</ul>
		</div>
		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNodesData } from "@vue-flow/core";
	import { ref, onMounted } from "vue";
	import EditorWithFullscreen from "@/components/utils/editor_with_fullscreen.vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { useI18n } from "vue-i18n";
	import { Error } from "@/utils/notify";
	const { t } = useI18n();
	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let contextVars = ref(getLinkedVar(prop.id));
	const tempKeys = ref<{ [key: string]: string }>({});

	onMounted(() => {
		if (nodeData.value) {
			if (!nodeData.value.data.input.params) {
				nodeData.value.data.input.params = {};
			}
			Object.keys(nodeData.value.data.input.params).forEach((key) => {
				tempKeys.value[key] = key;
			});
		}
	});

	const updateKey = (oldKey: string, newKey: string) => {
		if (nodeData.value && oldKey !== newKey) {
			if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(newKey)) {
				Error(t("invalid_variable_name"));
				tempKeys.value[oldKey] = oldKey;
				return;
			}
			// 保存原始键的顺序
			const originalKeys = Object.keys(nodeData.value.data.input.params);
			const value = nodeData.value.data.input.params[oldKey];

			// 创建一个新对象，按照原始顺序重新添加所有属性
			const newParams: Record<string, any> = {};
			originalKeys.forEach((key) => {
				if (key === oldKey) {
					// 用新键替换旧键，但保持在相同位置
					newParams[newKey] = { value: value?.value || "" };
				} else {
					newParams[key] = nodeData.value!.data.input.params[key];
				}
			});

			// 替换整个对象以保持顺序
			nodeData.value.data.input.params = newParams;
			tempKeys.value[newKey] = newKey;
			delete tempKeys.value[oldKey];
		}
	};

	const addInput = () => {
		if (nodeData.value) {
			const newKey = `input${Object.keys(nodeData.value.data.input.params).length + 1}`;
			nodeData.value.data.input.params[newKey] = { value: "" };
			tempKeys.value[newKey] = newKey;
		}
	};

	const removeInput = (key: string) => {
		if (nodeData.value) {
			delete nodeData.value.data.input.params[key];
			delete tempKeys.value[key];
		}
	};
</script>
<style scoped></style>
