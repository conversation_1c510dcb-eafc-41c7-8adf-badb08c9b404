<template>
	<BaseNode>
		<template #body="{ data }">
			<p class="w-full flex items-center">
				<component
					v-if="iconComponent"
					:is="iconComponent"
					class="w-5 h-5 align-middle text-white bg-accent rounded-md p-1 box-border shrink-0"
				/>
				<span class="ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
					data.name
				}}</span>
			</p>
			<p class="m-0 mt-1 text-xs font-thin">{{ t("model") }}: {{ data.input.model }}</p>
			<p class="m-0 mt-1 text-xs font-thin break-all whitespace-normal">
				{{ t("prompt") }}:
				{{
					(data.input.prompt.slice(0, 50) || "-") +
					(data.input.prompt.length > 50 ? "..." : "")
				}}
			</p>
			<p
				v-if="data.input.mcp_servers && data.input.mcp_servers.length > 0"
				class="m-0 mt-1 text-xs font-thin"
			>
				{{ t("mcp_servers") }}: {{ getMCPServerNames(data.input.mcp_servers) }}
			</p>
		</template>
	</BaseNode>
</template>
<script setup lang="ts">
	import BaseNode from "@/components/utils/base_node.vue";
	import { Nodes } from "./nodes.ts";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const iconComponent = Nodes["llm_agent"].icon || null;

	// 定义 MCP 服务器接口
	interface MCPServer {
		name: string;
	}

	// 获取 MCP 服务器名称列表
	function getMCPServerNames(servers: MCPServer[]): string {
		return servers.map((server) => server.name).join(", ");
	}
</script>
<style scoped></style>
