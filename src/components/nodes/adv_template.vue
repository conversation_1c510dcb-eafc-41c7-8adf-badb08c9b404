<template>
	<BaseNode>
		<template #body="{ data }">
			<p class="w-full flex items-center">
				<component
					v-if="iconComponent"
					:is="iconComponent"
					class="w-5 h-5 align-middle text-white bg-neutral rounded-md p-1 box-border shrink-0"
				/>
				<span class="ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
					data.name
				}}</span>
			</p>
		</template>
	</BaseNode>
</template>
<script setup lang="ts">
	import BaseNode from "@/components/utils/base_node.vue";
	import { Nodes } from "./nodes.ts";
	const iconComponent = Nodes["adv_template"].icon || null;
</script>
<style scoped></style>
