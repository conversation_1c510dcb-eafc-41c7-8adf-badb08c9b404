<template>
	<div v-if="nodeData" class="box-border">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<div>
			<p class="mt-4">{{ t("http_address") }}:</p>
			<p class="mb-2 align-middle">
				<input
					id="url_from_var"
					type="radio"
					name="url_from"
					class="radio radio-primary align-middle radio-xs"
					v-model="url_from"
					value="var"
				/>
				<label for="url_from_var" class="align-middle ml-1 text-sm">{{
					t("from_variable")
				}}</label>

				<input
					id="url_from_input"
					type="radio"
					name="url_from"
					v-model="url_from"
					value="input"
					class="ml-3 radio radio-primary align-middle radio-xs"
				/><label for="url_from_input" class="align-middle ml-1 text-sm">{{
					t("direct_input")
				}}</label>
			</p>
			<select
				class="select select-sm select-bordered w-1/4 text-sm"
				v-model="nodeData.data.input.method"
			>
				<option disabled selected value="">--{{ t("pls-select") }}--</option>
				<option v-for="i in ['GET', 'POST', 'PUT', 'DELETE']" :value="i" :key="i">
					{{ i }}
				</option>
			</select>
			<input
				v-if="url_from == 'input'"
				type="text"
				:placeholder="t('input_url')"
				v-model="nodeData.data.input.url"
				class="input input-sm input-bordered w-3/4 text-sm"
			/>
			<select
				v-if="url_from == 'var'"
				class="select select-sm select-bordered w-3/4"
				v-model="nodeData.data.input.url"
			>
				<option disabled selected value="">--{{ t("pls-select") }}--</option>
				<option v-for="i in vars" :value="'$' + i.node + '.' + i.name">
					${{ i.node + "." + i.name }}
				</option>
			</select>
		</div>

		<div>
			<p class="mt-4 mb-2">
				{{ t("request_headers") }}:
				<button
					class="btn btn-xs float-right"
					@click="nodeData.data.input.headers.push({ key: '', value: '' })"
				>
					+ {{ t("add") }}
				</button>
			</p>
			<div v-if="nodeData.data.input.headers.length <= 0">
				<p class="text-xs text-gray-400 text-center bg-gray-100 p-2 rounded-lg">
					{{ t("request_headers_empty") }}
				</p>
			</div>
			<div
				v-for="(i, index) in nodeData.data.input.headers"
				:key="index"
				class="relative mt-1 box-border"
			>
				<input
					placeholder="key"
					type="text"
					v-model="i.key"
					class="input input-bordered input-sm w-2/5 box-border text-sm"
				/>
				<input
					placeholder="value"
					type="text"
					v-model="i.value"
					class="input input-bordered input-sm w-3/5 box-border text-sm"
				/>
				<button
					class="btn btn-xs absolute right-1 top-1"
					@click="nodeData.data.input.headers.splice(index, 1)"
				>
					{{ t("delete") }}
				</button>
			</div>
		</div>

		<div v-if="nodeData.data.input.method != 'GET'">
			<p class="mt-4 mb-2">{{ t("request_body") }}:</p>
			<EditorWithFullscreen
				v-model="nodeData.data.input.body"
				:vars="vars"
				editor-type="code"
			/>
		</div>

		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNode, useNodesData } from "@vue-flow/core";
	import { ref, watch } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import EditorWithFullscreen from "@/components/utils/editor_with_fullscreen.vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	let prop = defineProps(["id"]);
	let nodeData = useNodesData(prop.id);
	let url_from = ref("var");
	if (nodeData.value && nodeData.value.data && nodeData.value.data.input) {
		let url = nodeData.value.data.input.url || "";
		if (url.startsWith("http")) {
			url_from.value = "input";
		}
		if (url.startsWith("$")) {
			url_from.value = "var";
		}
	}

	watch(url_from, () => {
		if (nodeData.value) {
			nodeData.value.data.input.url = "";
		}
	});

	watch(
		() => nodeData.value?.data.input.method,
		() => {
			if (nodeData.value) {
				nodeData.value.data.input.body = "";

				if (nodeData.value?.data.input.method != "GET") {
					if (!nodeData.value.data.input.headers) {
						nodeData.value.data.input.headers = [];
					}
					const hasContentType = nodeData.value.data.input.headers.some(
						(header: { key: string; value: string }) =>
							header.key.toLowerCase() === "content-type"
					);
					if (!hasContentType) {
						nodeData.value.data.input.headers.push({
							key: "content-type",
							value: "application/json",
						});
					}
				}
			}
		}
	);

	if (nodeData.value && !nodeData.value.data.input.url) {
		nodeData.value.data.input.url = "";
	}
	let vars = ref(getLinkedVar(prop.id));
</script>
<style scoped></style>
