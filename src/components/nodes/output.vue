<template>
	<div class="myinput relative hover:bg-gray-200">
		<div>
			<p class="w-full flex items-center">
				<component
					v-if="iconComponent"
					:is="iconComponent"
					class="w-5 h-5 align-middle text-white bg-primary rounded-md p-1 box-border shrink-0"
				/>
				<span class="ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
					data.name
				}}</span>
			</p>
			<p class="m-0 mt-1 text-xs font-thin">
				{{ data.input.from }}
			</p>
		</div>
		<Handle type="target" :position="Position.Left" />
	</div>
</template>
<script setup lang="ts">
	import { Handle, Position, useNode } from "@vue-flow/core";
	import { Nodes } from "./nodes.ts";
	const iconComponent = Nodes["out"].icon || null;
	const node = useNode();
	const data = node.node.data;
</script>
<style scoped>
	.myinput {
		font-weight: bold;
		display: flex;
		align-items: center;
		padding: 10px;
		font-size: 13px;
		border-radius: 15px;
		width: 200px;
		min-height: 60px;
		max-width: 300px;
		background-color: #fff;
		box-shadow: 1px 4px 7px 0px rgba(0, 0, 0, 0.1);
		transition: 0.3s;
		border: 1px solid #f8f8f8;
	}
	.myinput:hover {
		background-color: #f8f8f8;
		@apply shadow-xl;
	}
	.inputHandle {
		@apply h-5 w-5 bg-blue-300 rounded-full hover:bg-blue-600 transition-all;
	}
	.inputHandle::before {
		content: "+";
		@apply text-white text-center absolute flex justify-center items-center h-full w-full cursor-pointer;
	}
</style>
