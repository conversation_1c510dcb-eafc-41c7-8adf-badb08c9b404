<template>
	<div class="condition-node relative hover:bg-gray-200 w-[450px]">
		<p class="w-full flex items-center">
			<component
				v-if="iconComponent"
				:is="iconComponent"
				class="w-5 h-5 align-middle text-white bg-accent rounded-md p-1 box-border shrink-0"
			/>
			<span class="ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
				data.name
			}}</span>
		</p>
		<p class="m-0 mt-1 text-xs font-thin">
			{{ t("condition_content") }}: {{ data.input.condition_content || "--" }}
		</p>

		<p
			class="m-0 text-xs font-thin w-full h-[50px]"
			:key="i.id"
			v-for="(i, index) in data.input.options"
		></p>

		<Handle type="target" :position="Position.Left" />
	</div>
</template>

<script setup lang="ts">
	import { useNodeId, useVueFlow } from "@vue-flow/core";
	import { watch, nextTick } from "vue";
	import { Handle, Position, useNode } from "@vue-flow/core";
	import { Nodes } from "./nodes.ts";
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();
	const iconComponent = Nodes["condition"].icon || null;

	const node = useNode();
	const data = node.node.data;

	let { addNodes, updateNode, findNode, nodes, removeNodes } = useVueFlow();
	let currentNodeID = useNodeId();

	watch(
		() => data.input.options,
		(newOptions) => {
			// 找到当前节点的子节点
			let currentChild = nodes.value
				.filter((i) => i.parentNode == currentNodeID)
				.map((e) => e.id);

			// 添加新选项
			let valNotInCurrentChild = newOptions.filter(
				(i: any) => !currentChild.includes(i.id)
			);
			valNotInCurrentChild.forEach((option: any) => {
				console.log(option);
				addNodes({
					id: option.id,
					type: "condition_class",
					position: { x: 10, y: 60 + currentChild.length * 50 },
					label: option.condition_type + ":" + option.value,
					parentNode: currentNodeID,
					extent: "parent",
					draggable: false,
					selectable: false,
					focusable: false,
					targetPosition: Position.Right,
					data: {
						input: option,
						name: option.condition_type + ":" + option.value,
					},
				});
			});

			// 删除不再存在的选项
			currentChild = nodes.value
				.filter((i) => i.parentNode == currentNodeID)
				.map((e) => e.id);
			let valInCurrentChild = currentChild.filter(
				(i: any) => !newOptions.map((i: any) => i.id).includes(i)
			);
			valInCurrentChild.forEach((i: any) => {
				removeNodes([i]);
			});

			// 更新现有选项和位置
			newOptions.forEach((option: any, index: number) => {
				let node = findNode(option.id);
				if (node) {
					node.data.input = option;
					node.data.name = option.condition_type + ":" + option.value;
					node.label = option.condition_type + ":" + option.value;
					// 更新节点位置以反映新的顺序
					node.position = { x: 10, y: 60 + index * 50 };
					updateNode(node.id, node);
				}
			});

			// 强制更新节点位置
			nextTick(() => {
				nodes.value
					.filter((node) => node.parentNode === currentNodeID)
					.forEach((node) => {
						const index = newOptions.findIndex((opt: any) => opt.id === node.id);
						if (index !== -1) {
							node.position = { x: 10, y: 60 + index * 50 };
							updateNode(node.id, node);
						}
					});
			});
		},
		{ deep: true }
	);
</script>

<style scoped>
	.condition-node {
		font-weight: bold;
		display: flex;
		padding: 10px;
		font-size: 13px;
		border-radius: 15px;
		width: 200px;
		min-height: 60px;
		min-width: 300px;
		max-width: 600px;
		background-color: #fff;
		box-shadow: 1px 4px 7px 0px rgba(0, 0, 0, 0.1);
		transition: 0.3s;
		border: 1px solid #f8f8f8;
		flex-direction: column;
		justify-content: start;
		align-items: start;
		padding-bottom: 10px; /* 为子节点留出空间 */
	}

	.condition-node:hover {
		background-color: #f8f8f8;
		@apply shadow-xl;
	}

	.child-nodes {
		position: relative;
		width: 100%;
	}
</style>
