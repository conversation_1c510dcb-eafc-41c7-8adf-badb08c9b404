<template>
	<BaseNode>
		<template #body="{ data }">
			<p class="w-full flex items-center">
				<component
					v-if="iconComponent"
					:is="iconComponent"
					class="w-5 h-5 align-middle text-white bg-blue-500 rounded-md p-1 box-border shrink-0"
				/>
				<span class="ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
					data.name
				}}</span>
			</p>
			<p class="m-0 mt-1 text-xs font-thin" v-if="data?.input?.plugin?.desc">
				{{ data.input.plugin.desc }}
			</p>
			<p class="m-0 mt-1 text-xs font-thin" v-else>{{ t("custom_plugin") }}</p>
		</template>
	</BaseNode>
</template>
<script setup lang="ts">
	import BaseNode from "@/components/utils/base_node.vue";
	import { Nodes } from "./nodes.ts";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const iconComponent = Nodes["plugin"].icon || null;
</script>
<style scoped></style>
