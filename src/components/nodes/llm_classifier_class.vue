<template>
	<div
		class="relative w-[280px] py-2 px-1 hover:bg-gray-200 transition-all rounded-md border border-solid border-gray-300"
	>
		<div
			class="text-sm font-bold text-neutral text-ellipsis overflow-hidden whitespace-nowrap truncate"
		>
			{{ data.input.class }}
		</div>
		<Add @showOptions="showOptions"></Add>
	</div>
</template>
<script setup lang="ts">
	import Add from "@/components/utils/add.vue";
	import { useNode, useNodesData } from "@vue-flow/core";

	const emit = defineEmits(["showOptions"]);
	const showOptions = (id: any, data: any, e: MouseEvent) => {
		emit("showOptions", id, data, e);
	};

	const node = useNode();
	const data = node.node.data;
</script>
<style scoped></style>
