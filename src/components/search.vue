<template>
	<teleport to="body">
		<div
			v-if="isOpen"
			class="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-start justify-center pt-4 sm:pt-16"
			@click.self="$emit('close')"
		>
			<div class="bg-white w-full max-w-2xl rounded-lg shadow-lg mx-2 sm:mx-0">
				<div class="p-3 sm:p-4 border-gray-200 flex items-center">
					<MagnifyingGlassIcon
						class="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 mr-2 sm:mr-3"
					/>
					<input
						ref="searchInput"
						v-model="searchTerm"
						type="text"
						:placeholder="
							t('search-workflow') + '... (' + (isMac ? '⌘K' : 'Ctrl+K') + ')'
						"
						@input="handleSearch"
						@keydown="handleKeyDown"
						class="flex-grow outline-none text-base sm:text-lg"
					/>
					<button
						@click="$emit('close')"
						class="ml-2 text-gray-400 hover:text-gray-600"
					>
						<XMarkIcon class="w-4 h-4 sm:w-5 sm:h-5" />
					</button>
				</div>
				<div class="results-container">
					<ul v-if="results.length > 0" class="py-2 overflow-y-auto max-h-60">
						<li
							v-for="(result, index) in results"
							:key="result.uuid"
							:class="[
								'px-3 sm:px-4 py-2 hover:bg-gray-100 cursor-pointer flex flex-col sm:flex-row sm:justify-between',
								{ 'bg-gray-100': index === selectedIndex },
							]"
							@click="handleResultClick(result)"
							@mouseover="selectedIndex = index"
						>
							<div class="mb-2 sm:mb-0">
								<h3 class="font-semibold text-sm sm:text-base">
									{{ result.title }}
								</h3>
								<p class="text-xs sm:text-sm text-gray-600">
									{{ result.description }}
								</p>
							</div>
							<div class="flex justify-end max-sm:justify-start">
								<button
									class="btn btn-xs"
									@click.stop="handleResultClick(result)"
								>
									{{ t("run") }}
								</button>
								<button
									class="btn btn-xs ml-1"
									@click.stop="handelResultEdit(result)"
								>
									{{ t("edit") }}
								</button>
							</div>
						</li>
					</ul>
					<div
						v-else-if="isLoading"
						class="p-3 sm:p-4 text-center text-gray-500 text-sm sm:text-base"
					>
						{{ t("searching") }}
					</div>
					<div
						v-else-if="searchTerm && !isLoading"
						class="p-3 sm:p-4 text-center text-gray-500 text-sm sm:text-base"
					>
						{{ t("no-search-result") }}
					</div>
				</div>
			</div>
		</div>
	</teleport>
</template>

<script lang="ts">
	import { ref, watch, nextTick } from "vue";
	import { debounce } from "lodash-es";
	import { getProjects, type Project } from "@/api/projects";
	import { useRouter } from "vue-router";
	import { useI18n } from "vue-i18n";
	import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/vue/24/outline";
	interface searchResult {
		title: string;
		description: string;
		uuid: string;
	}

	export default {
		name: "GlobalSearch",
		props: {
			isOpen: {
				type: Boolean,
				required: true,
			},
		},
		components: {
			MagnifyingGlassIcon,
			XMarkIcon,
		},
		emits: ["close", "searchClick"],
		setup(props, { emit }) {
			const searchTerm = ref("");
			const results = ref<searchResult[]>([]);
			const searchInput = ref(null);
			const isLoading = ref(false);
			const selectedIndex = ref(-1);
			const router = useRouter();
			const isMac = ref(/Mac|iPod|iPhone|iPad/.test(navigator.platform));

			const search = async (term: string) => {
				if (!term) {
					results.value = [];
					selectedIndex.value = -1;
					return;
				}

				isLoading.value = true;
				selectedIndex.value = -1; // Reset selection when starting a new search
				try {
					const resp = await getProjects(1, 50, term);
					results.value = resp.list.map((item: any) => ({
						uuid: item.uuid,
						title: item.name,
						description: item.description,
					}));
					// If we have results, set the first one as selected
					if (results.value.length > 0) {
						selectedIndex.value = 0;
					}
				} catch (error) {
					console.error("搜索出错:", error);
					results.value = [];
					selectedIndex.value = -1;
				} finally {
					isLoading.value = false;
				}
			};

			const debouncedSearch = debounce(search, 300);

			const handleSearch = () => {
				// We don't need to reset selectedIndex here as it's handled in the search function
				debouncedSearch(searchTerm.value);
			};

			const handleResultClick = (result: searchResult) => {
				emit("searchClick", result);
				emit("close");
				router.push({ name: "runtime", params: { id: result.uuid } });
			};

			const handelResultEdit = (result: searchResult) => {
				router.push({ name: "workflow", params: { id: result.uuid } });
				emit("close");
			};

			const handleKeyDown = (event: any) => {
				// Don't handle navigation keys when loading or no results
				if (
					(event.key === "ArrowDown" || event.key === "ArrowUp") &&
					!isLoading.value &&
					results.value.length > 0
				) {
					event.preventDefault();
					if (event.key === "ArrowDown") {
						// If selectedIndex is -1 or invalid, start from the first item
						if (
							selectedIndex.value < 0 ||
							selectedIndex.value >= results.value.length - 1
						) {
							selectedIndex.value = 0;
						} else {
							selectedIndex.value++;
						}
					} else if (event.key === "ArrowUp") {
						// If selectedIndex is -1 or invalid, start from the last item
						if (selectedIndex.value <= 0) {
							selectedIndex.value = results.value.length - 1;
						} else {
							selectedIndex.value--;
						}
					}
				} else if (
					event.key === "Enter" &&
					selectedIndex.value !== -1 &&
					results.value.length > 0
				) {
					event.preventDefault();
					handleResultClick(results.value[selectedIndex.value]);
				} else if (event.key === "Escape") {
					emit("close");
				}
			};

			watch(
				() => props.isOpen,
				(newVal) => {
					if (newVal) {
						results.value = [];
						searchTerm.value = "";
						selectedIndex.value = -1;
						nextTick(() => {
							if (searchInput.value) {
								// @ts-ignore
								searchInput.value.focus();
							}
						});
					}
				}
			);
			const { t } = useI18n();

			return {
				searchTerm,
				results,
				handleSearch,
				searchInput,
				isLoading,
				handleResultClick,
				handelResultEdit,
				handleKeyDown,
				selectedIndex,
				isMac,
				t,
			};
		},
	};
</script>

<style scoped>
	.results-container {
		max-height: 60vh;
		overflow-y: auto;
	}

	.results-container ul {
		max-height: 100%;
	}

	.results-container::-webkit-scrollbar {
		width: 6px;
	}

	.results-container::-webkit-scrollbar-track {
		background: #f1f1f1;
	}

	.results-container::-webkit-scrollbar-thumb {
		background: #888;
		border-radius: 3px;
	}

	.results-container::-webkit-scrollbar-thumb:hover {
		background: #555;
	}
</style>
