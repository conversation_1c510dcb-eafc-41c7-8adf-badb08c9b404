<template>
	<div class="relative px-3 max-sm:px-0">
		<button
			ref="buttonRef"
			@click="toggleUserMenu"
			class="flex items-center w-full text-sm py-2 px-3 rounded-md hover:bg-primary hover:text-base-100 transition-colors duration-200 mb-3 max-sm:mb-0"
			:class="{
				'sm:!py-2': props.isCollapsed,
				'sm:!px-0': props.isCollapsed,
				'justify-center': props.isCollapsed,
				'!w-full': !props.isCollapsed,
			}"
		>
			<UserCircleIcon class="w-5 h-5 flex-shrink-0" />
			<span v-if="!props.isCollapsed" class="ml-2 truncate max-w-[120px]">
				{{ userStore.userRef?.username || t("user") }}
			</span>
		</button>
		<Teleport to="body">
			<div
				v-if="showUserMenu"
				class="fixed inset-0 bg-black bg-opacity-0 z-40"
				@click="closeUserMenu"
				@mousemove="handleMouseMove"
			>
				<div
					v-motion-fade-visible
					class="absolute bg-white shadow-md rounded-md py-2"
					:style="popupStyle"
					@click.stop
				>
					<div class="px-3 py-2 text-xs">
						<p>{{ t("email") }}: {{ userStore.userRef?.email || t("not-set") }}</p>
						<p>{{ t("credits") }}: {{ userStore.userRef?.credits || "0" }}</p>
					</div>
					<div
						class="flex items-center px-3 py-2 text-sm text-left bg-base-100 hover:bg-gray-100 transition-colors duration-200 w-full"
					>
						<LanguageIcon class="w-3 h-3 mr-2 align-middle" />
						<select
							@change="(e) => changeLanguage((e.target as HTMLSelectElement).value)"
							class="px-0"
						>
							<option value="en" :selected="locale === 'en'">English</option>
							<option value="zh" :selected="locale === 'zh'">中文</option>
						</select>
					</div>
					<button
						@click="() => router.push('setting')"
						class="flex items-center w-full text-left px-3 py-2 bg-base-100 text-sm hover:bg-gray-100 transition-colors duration-200"
					>
						<CogIcon class="w-3 h-3 mr-2 align-middle" />
						<span class="align-middle">{{ t("settings") }}</span>
					</button>

					<button
						@click="onLogout"
						class="flex items-center w-full text-left px-3 py-2 bg-base-100 text-sm hover:bg-gray-100 transition-colors duration-200"
					>
						<PowerIcon class="w-3 h-3 mr-2 align-middle" />
						<span class="align-middle">{{ t("logout") }}</span>
					</button>
				</div>
			</div>
		</Teleport>
	</div>
</template>

<script setup lang="ts">
	import { useUserStore } from "@/stores/user";
	import { ref, watch, computed, onMounted, onUnmounted } from "vue";
	import { logout, me } from "@/api/user";
	import { UserCircleIcon, PowerIcon, CogIcon, LanguageIcon } from "@heroicons/vue/24/solid";
	import { useRouter } from "vue-router";
	import { useI18n } from "vue-i18n";
	const { t, locale } = useI18n();
	const props = defineProps({
		isCollapsed: {
			type: Boolean,
			default: false,
		},
	});

	const userStore = useUserStore();
	const showUserMenu = ref(false);
	const buttonRef = ref<HTMLButtonElement | null>(null);
	const isMouseInSafeZone = ref(false);
	const router = useRouter();

	const popupStyle = computed(() => {
		if (!buttonRef.value) return {};
		const rect = buttonRef.value.getBoundingClientRect();
		const windowWidth = window.innerWidth;
		const popupWidth = 200; // 假设弹出层宽度为200px

		let left = rect.left;
		if (left + popupWidth > windowWidth) {
			left = windowWidth - popupWidth;
		}

		return {
			top: `${rect.bottom - 180}px`,
			left: `${left}px`,
			minWidth: `${popupWidth}px`,
		};
	});

	const toggleUserMenu = () => {
		showUserMenu.value = !showUserMenu.value;
		if (showUserMenu.value) {
			me().then((res) => {
				userStore.setUser(res);
			});
		}
	};

	const closeUserMenu = () => {
		if (!isMouseInSafeZone.value) {
			showUserMenu.value = false;
		}
	};

	const handleMouseMove = (event: MouseEvent) => {
		if (!buttonRef.value) return;
		const rect = buttonRef.value.getBoundingClientRect();
		const popupRect = document.querySelector(".bg-white")?.getBoundingClientRect();

		if (!popupRect) return;

		isMouseInSafeZone.value =
			(event.clientX >= rect.left &&
				event.clientX <= rect.right &&
				event.clientY >= rect.top &&
				event.clientY <= popupRect.bottom) ||
			(event.clientX >= popupRect.left &&
				event.clientX <= popupRect.right &&
				event.clientY >= popupRect.top &&
				event.clientY <= popupRect.bottom);
	};

	const onLogout = () => {
		if (!confirm(t("confirm-logout"))) {
			return;
		}
		logout().then(() => {
			window.location.href = "/";
		});
	};

	const changeLanguage = (lang: string) => {
		locale.value = lang;
		localStorage.setItem("locale", lang);
	};
</script>
