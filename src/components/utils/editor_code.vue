<template>
	<div
		ref="editorContainer"
		class="editor-container"
		:class="{ 'h-full': isFullHeight }"
	></div>
</template>

<script setup lang="ts">
	import { ref, onMounted, watch, onBeforeUnmount, computed, useAttrs } from "vue";
	const $attrs = useAttrs();
	import * as monaco from "monaco-editor";
	import editorWorker from "monaco-editor/esm/vs/editor/editor.worker?worker";
	import jsonWorker from "monaco-editor/esm/vs/language/json/json.worker?worker";
	import cssWorker from "monaco-editor/esm/vs/language/css/css.worker?worker";
	import htmlWorker from "monaco-editor/esm/vs/language/html/html.worker?worker";
	import tsWorker from "monaco-editor/esm/vs/language/typescript/ts.worker?worker";

	self.MonacoEnvironment = {
		getWorker(_, label) {
			if (label === "json") {
				return new jsonWorker();
			}
			if (label === "css" || label === "scss" || label === "less") {
				return new cssWorker();
			}
			if (label === "html" || label === "handlebars" || label === "razor") {
				return new htmlWorker();
			}
			if (label === "typescript" || label === "javascript") {
				return new tsWorker();
			}
			return new editorWorker();
		},
	};

	const props = defineProps({
		modelValue: String,
		vars: Object,
		highlightVariables: {
			type: Boolean,
			default: true,
		},
	});

	// 检查是否应该使用全高度模式
	const isFullHeight = computed(() => {
		return (
			$attrs.class && typeof $attrs.class === "string" && $attrs.class.includes("h-full")
		);
	});

	const emit = defineEmits(["update:modelValue"]);

	const editorContainer = ref(null);
	let editor: monaco.editor.IStandaloneCodeEditor;
	let disposables: monaco.IDisposable[] = [];

	// 每个实例都有自己的建议列表
	const suggestions: { label: string; insertText: string; detail: any }[] = [
		// { label: "你好.name", insertText: "你好.name", detail: "问候" },
	];

	// 为每个实例创建唯一的语言 ID
	const uniqueLanguageId = `custom-markdown-${Date.now()}-${Math.random()
		.toString(36)
		.slice(2, 11)}`;

	onMounted(() => {
		// 为每个实例注册唯一的语言
		monaco.languages.register({ id: uniqueLanguageId });

		let root: any[] = [
			[/^#.*/, "keyword"],
			[/\*\*.*\*\*/, "strong"],
			[/_.*_/, "emphasis"],
			[/\*[\^\*].*?\*/, "emphasis"],
			[/\[.*?\]\(.*?\)/, "link"],
			[/!\[.*?\]\(.*?\)/, "image"],
			[/`.*?`/, "inline-code"],
			[/^>.*/, "quote"],
			[/^\s*[-*+]\s+.*/, "list"],
			[/^\s*\d+\.\s+.*/, "list"],
			[/^(---|\*\*\*|___)$/, "hr"],
			[/^\|.*\|$/, "table"],
			[/^\s*:?-+:?\s*$/, "table"],
		];
		if (props.highlightVariables) {
			root.push([/(\$[\w\u4e00-\u9fa5]+)(\.?[\w\u4e00-\u9fa5]+)*/, "variable"]);
		}
		// 为该语言设置 Monarch 标记提供程序
		disposables.push(
			monaco.languages.setMonarchTokensProvider(uniqueLanguageId, {
				tokenizer: {
					root: root,
				},
			})
		);

		// 为该语言注册完成项提供程序
		disposables.push(
			monaco.languages.registerCompletionItemProvider(uniqueLanguageId, {
				triggerCharacters: [".", "$"],
				provideCompletionItems: (model, position) => {
					const textUntilPosition = model.getValueInRange({
						startLineNumber: position.lineNumber,
						startColumn: 1,
						endLineNumber: position.lineNumber,
						endColumn: position.column,
					});
					// 如果变量高亮被禁用，则不提供变量补全
					if (!props.highlightVariables) {
						return { suggestions: [] };
					}
					const match = textUntilPosition.match(/\$[\w\u4e00-\u9fa5\.]*$/);
					if (!match) {
						return { suggestions: [] };
					}
					return {
						suggestions: suggestions.map((s) => ({
							label: s.label,
							kind: monaco.languages.CompletionItemKind.Variable,
							insertText: s.insertText,
							detail: s.detail,
							range: {
								startLineNumber: position.lineNumber,
								endLineNumber: position.lineNumber,
								startColumn: position.column - match[0].length + 1,
								endColumn: position.column,
							},
						})),
					};
				},
			})
		);

		// 创建编辑器实例
		editor = monaco.editor.create(editorContainer.value!, {
			value: props.modelValue,
			language: uniqueLanguageId,
			theme: "vs-light",
			automaticLayout: true,
			fontSize: 14,
			lineNumbers: "on",
			lineNumbersMinChars: 1,
			minimap: { enabled: false },
			scrollbar: {
				vertical: "visible",
				horizontal: "visible",
			},
			suggest: {
				insertMode: "insert",
				snippetsPreventQuickSuggestions: false,
			},
			unicodeHighlight: {
				ambiguousCharacters: false,
			},
		});

		editor.onDidChangeModelContent(() => {
			emit("update:modelValue", editor.getValue());
		});

		props.vars!.forEach((v: { node: string; name: string; desc: any }) => {
			suggestions.push({
				label: v.node + "." + v.name,
				insertText: v.node + "." + v.name,
				detail: v.desc,
			});
		});
	});

	watch(
		() => props.modelValue,
		(newValue) => {
			if (editor && newValue !== editor.getValue()) {
				editor.setValue(newValue!);
			}
		}
	);

	// 清理资源
	onBeforeUnmount(() => {
		if (editor) {
			editor.dispose();
		}
		disposables.forEach((d) => d.dispose());
	});
</script>

<style scoped>
	.editor-container {
		width: 100%;
		height: 200px;
		min-width: 300px;
		position: relative;
		border: 1px solid #e7e5e5;
		margin-top: 10px;
		margin-bottom: 10px;
	}

	.editor-container.h-full {
		height: 100%;
		margin: 0;
	}
</style>
<style>
	.monaco-editor .suggest-widget {
		z-index: 100 !important;
		right: auto;
		width: 300px !important;
		left: 40px !important;
	}
	/* 变量样式 */
	.editor-container .mtk23 {
		@apply bg-primary text-white;
		border-radius: 5px;
		padding: 5px 5px;
		font-size: 12px !important;
	}
</style>
