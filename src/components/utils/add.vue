<template>
	<Handle
		:id="prop.id"
		@click.prevent.stop="(e) => showOptions(e)"
		:class="{ inputHandle: showAdd }"
		type="source"
		:position="Position.Right"
	/>
</template>
<script setup lang="ts">
	import { ref, watch } from "vue";
	import { Handle, Position, useHandle, useNode } from "@vue-flow/core";

	const prop = defineProps(["id"]);

	const showAdd = ref(true);
	const { connectedEdges, id, node } = useNode();
	watch(connectedEdges, () => {
		showAdd.value =
			connectedEdges.value.findIndex((item: any) => item.source === id) == -1;
		node.connectable = showAdd.value;
	});
	showAdd.value = connectedEdges.value.findIndex((item: any) => item.source === id) == -1;
	const emit = defineEmits(["showOptions"]);
	const showOptions = (e: MouseEvent) => {
		if (!showAdd.value) return;
		emit("showOptions", id, {}, e);
	};
</script>
<style scoped>
	.inputHandle {
		@apply h-5 w-5 bg-primary rounded-full hover:bg-neutral transition-all;
	}
	.inputHandle::before {
		content: "+";
		@apply text-white text-center absolute flex justify-center items-center h-[90%] w-full cursor-pointer;
	}
</style>
