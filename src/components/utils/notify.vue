<template>
	<NotificationGroup group="notify">
		<div
			class="fixed inset-0 flex items-start justify-end p-6 px-4 py-6 pointer-events-none z-[10000]"
		>
			<div class="w-full max-w-sm">
				<Notification
					v-slot="{ notifications, close }"
					enter="transform ease-out duration-300 transition"
					enter-from="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-4"
					enter-to="translate-y-0 opacity-100 sm:translate-x-0"
					leave="transition ease-in duration-500"
					leave-from="opacity-100"
					leave-to="opacity-0"
					move="transition duration-500"
					move-delay="delay-300"
				>
					<div
						v-for="notification in notifications"
						:key="notification.id"
						class="pointer-events-auto"
					>
						<div
							v-if="notification.type === 'success'"
							class="flex relative w-full max-w-sm mx-auto mt-4 overflow-hidden bg-white rounded-lg shadow-md"
						>
							<div class="flex items-center justify-center w-12 bg-green-500">
								<svg
									class="w-6 h-6 text-white fill-current"
									viewBox="0 0 40 40"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path
										d="M20 3.33331C10.8 3.33331 3.33337 10.8 3.33337 20C3.33337 29.2 10.8 36.6666 20 36.6666C29.2 36.6666 36.6667 29.2 36.6667 20C36.6667 10.8 29.2 3.33331 20 3.33331ZM16.6667 28.3333L8.33337 20L10.6834 17.65L16.6667 23.6166L29.3167 10.9666L31.6667 13.3333L16.6667 28.3333Z"
									/>
								</svg>
							</div>

							<div class="px-4 py-2 -mx-3">
								<div class="ml-3 mr-5">
									<span class="font-semibold text-green-500">{{
										notification.title
									}}</span>
									<p class="text-sm text-gray-600">
										{{ notification.text }}
									</p>
								</div>
							</div>
							<button
								@click="close(notification.id)"
								class="absolute top-0 bottom-0 right-0 px-4 py-3"
							>
								<svg
									class="w-6 h-6 text-gray-500 fill-current hover:text-gray-600"
									role="button"
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 20 20"
								>
									<title>Close</title>
									<path
										d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"
									/>
								</svg>
							</button>
						</div>

						<div
							v-if="notification.type === 'info'"
							class="flex w-full relative max-w-sm mx-auto mt-4 overflow-hidden bg-white rounded-lg shadow-md"
						>
							<div class="flex items-center justify-center w-12 bg-blue-500">
								<svg
									class="w-6 h-6 text-white fill-current"
									viewBox="0 0 40 40"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path
										d="M20 3.33331C10.8 3.33331 3.33337 10.8 3.33337 20C3.33337 29.2 10.8 36.6666 20 36.6666C29.2 36.6666 36.6667 29.2 36.6667 20C36.6667 10.8 29.2 3.33331 20 3.33331ZM21.6667 28.3333H18.3334V25H21.6667V28.3333ZM21.6667 21.6666H18.3334V11.6666H21.6667V21.6666Z"
									/>
								</svg>
							</div>

							<div class="px-4 py-2 -mx-3">
								<div class="ml-3 mr-5">
									<span class="font-semibold text-blue-500">{{
										notification.title
									}}</span>
									<p class="text-sm text-gray-600">
										{{ notification.text }}
									</p>
								</div>
							</div>

							<button
								@click="close(notification.id)"
								class="absolute top-0 bottom-0 right-0 px-4 py-3"
							>
								<svg
									class="w-6 h-6 text-gray-500 fill-current hover:text-gray-600"
									role="button"
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 20 20"
								>
									<title>Close</title>
									<path
										d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"
									/>
								</svg>
							</button>
						</div>

						<div
							class="flex w-full relative max-w-sm mx-auto mt-4 overflow-hidden bg-white rounded-lg shadow-md"
							v-if="notification.type === 'warning'"
						>
							<div class="flex items-center justify-center w-12 bg-yellow-500">
								<svg
									class="w-6 h-6 text-white fill-current"
									viewBox="0 0 40 40"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path
										d="M20 3.33331C10.8 3.33331 3.33337 10.8 3.33337 20C3.33337 29.2 10.8 36.6666 20 36.6666C29.2 36.6666 36.6667 29.2 36.6667 20C36.6667 10.8 29.2 3.33331 20 3.33331ZM21.6667 28.3333H18.3334V25H21.6667V28.3333ZM21.6667 21.6666H18.3334V11.6666H21.6667V21.6666Z"
									/>
								</svg>
							</div>

							<div class="px-4 py-2 -mx-3">
								<div class="ml-3 mr-5">
									<span class="font-semibold text-yellow-500">{{
										notification.title
									}}</span>
									<p class="text-sm text-gray-600">
										{{ notification.text }}
									</p>
								</div>
							</div>

							<button
								@click="close(notification.id)"
								class="absolute top-0 bottom-0 right-0 px-4 py-3"
							>
								<svg
									class="w-6 h-6 text-gray-500 fill-current hover:text-gray-600"
									role="button"
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 20 20"
								>
									<title>Close</title>
									<path
										d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"
									/>
								</svg>
							</button>
						</div>

						<div
							class="flex w-full relative max-w-sm mx-auto mt-4 overflow-hidden bg-white rounded-lg shadow-md"
							v-if="notification.type === 'error'"
						>
							<div class="flex items-center justify-center w-12 bg-red-500">
								<svg
									class="w-6 h-6 text-white fill-current"
									viewBox="0 0 40 40"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path
										d="M20 3.36667C10.8167 3.36667 3.3667 10.8167 3.3667 20C3.3667 29.1833 10.8167 36.6333 20 36.6333C29.1834 36.6333 36.6334 29.1833 36.6334 20C36.6334 10.8167 29.1834 3.36667 20 3.36667ZM19.1334 33.3333V22.9H13.3334L21.6667 6.66667V17.1H27.25L19.1334 33.3333Z"
									/>
								</svg>
							</div>

							<div class="px-4 py-2 -mx-3">
								<div class="ml-3 mr-5">
									<span class="font-semibold text-red-500">{{
										notification.title
									}}</span>
									<p class="text-sm text-gray-600">
										{{ notification.text }}
									</p>
								</div>
							</div>

							<button
								@click="close(notification.id)"
								class="absolute top-0 bottom-0 right-0 px-4 py-3"
							>
								<svg
									class="w-6 h-6 text-gray-500 fill-current hover:text-gray-600"
									role="button"
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 20 20"
								>
									<title>Close</title>
									<path
										d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"
									/>
								</svg>
							</button>
						</div>
					</div>
				</Notification>
			</div>
		</div>
	</NotificationGroup>
</template>

<script setup lang="ts"></script>
