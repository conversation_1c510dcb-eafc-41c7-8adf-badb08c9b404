<template>
	<div
		class="absolute"
		:style="{ left: props.left + 'px', top: props.top + 'px' }"
		@keydown.esc="closeOptions"
	>
		<div class="pl-4">
			<div class="card base-100 shadow-xl rounded-lg overflow-hidden w-52">
				<div class="p-2 bg-base-100">
					<input
						v-model="searchQuery"
						type="text"
						:placeholder="t('search')"
						class="input input-sm input-bordered w-full max-w-xs"
						ref="searchInput"
						@focus="focusInput"
					/>
				</div>
				<ul class="menu flex-nowrap bg-base-100 overflow-y-scroll max-h-56">
					<template v-for="(group, groupName) in groupedOptions" :key="groupName">
						<li class="menu-title menu-xs pointer-events-none">
							<span>{{ groupName }}</span>
						</li>
						<li v-for="i in group" :key="i.label" @click="onOptionSelect(i)">
							<span>
								<component
									v-if="i.icon"
									:is="i.icon"
									class="w-5 h-5 align-middle text-white bg-gray-500 rounded-md p-1 box-border shrink-0"
								/>
								{{ i.nodeShowName }}
							</span>
						</li>
					</template>
				</ul>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { ref, computed, onMounted, onUnmounted } from "vue";
	import type { PropType } from "vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	const props = defineProps({
		left: {
			type: Number,
			default: 0,
		},
		top: {
			type: Number,
			default: 0,
		},
		options: {
			type: Array as PropType<
				{
					label: string;
					nodeShowName: string;
					type: string;
					plugin?: any;
					icon?: any;
					group: string;
				}[]
			>,
			required: true,
		},
	});

	const emit = defineEmits(["selected", "close"]);

	const searchQuery = ref("");
	const searchInput = ref<HTMLInputElement | null>(null);

	const filteredOptions = computed(() => {
		return props.options.filter(
			(option: (typeof props.options)[number]) =>
				option.label.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
				option.type.toLowerCase().includes(searchQuery.value.toLowerCase())
		);
	});

	const groupedOptions = computed(() => {
		const grouped: Record<string, (typeof props.options)[number][]> = {};
		filteredOptions.value.forEach((option: (typeof props.options)[number]) => {
			if (!grouped[option.group]) {
				grouped[option.group] = [];
			}
			grouped[option.group].push(option);
		});
		return grouped;
	});

	const onOptionSelect = (data: (typeof props.options)[number]) => {
		emit("selected", data);
	};

	const closeOptions = () => {
		emit("close");
	};

	const focusInput = () => {
		if (searchInput.value) {
			searchInput.value.focus();
		}
	};

	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === "Escape") {
			closeOptions();
		}
	};

	onMounted(() => {
		document.addEventListener("keydown", handleKeydown);
		// focusInput();
	});

	onUnmounted(() => {
		document.removeEventListener("keydown", handleKeydown);
	});
</script>
