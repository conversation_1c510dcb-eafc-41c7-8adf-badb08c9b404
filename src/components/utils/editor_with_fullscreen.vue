<template>
	<div class="relative group">
		<ComponentWait v-if="editorType === 'code'">
			<EditorCode 
				v-model="localModelValue" 
				:vars="vars" 
				:highlightVariables="highlightVariables"
				style="position: relative" 
			/>
		</ComponentWait>
		<ComponentWait v-else-if="editorType === 'ext'">
			<EditorExt 
				v-model="localModelValue" 
				:vars="vars" 
				:language="language"
				:class="editorClass"
				style="position: relative" 
			/>
		</ComponentWait>
		<button
			@click="openFullscreenEditor"
			class="absolute top-2 right-2 btn btn-xs btn-ghost opacity-0 group-hover:opacity-100 transition-opacity duration-200"
			:title="t('editor.fullscreen')"
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				class="h-4 w-4"
				fill="none"
				viewBox="0 0 24 24"
				stroke="currentColor"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5"
				/>
			</svg>
		</button>
	</div>

	<!-- 使用teleport将模态框传送到body -->
	<teleport to="body" v-if="showFullscreenEditor">
		<div
			class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
		>
			<div class="bg-white rounded-lg w-full max-w-4xl h-3/4 flex flex-col">
				<div class="flex justify-between items-center p-4 border-b">
					<h3 class="text-lg font-medium">{{ t("editor.title") }}</h3>
					<button
						@click="closeFullscreenEditor"
						class="btn btn-sm btn-ghost"
						:aria-label="t('editor.close')"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-5 w-5"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M6 18L18 6M6 6l12 12"
							/>
						</svg>
					</button>
				</div>
				<div class="flex-grow p-4 overflow-hidden">
					<ComponentWait v-if="editorType === 'code'">
						<EditorCode
							v-model="localModelValue"
							:vars="props.vars"
							:highlightVariables="highlightVariables"
							class="h-full"
						/>
					</ComponentWait>
					<ComponentWait v-else-if="editorType === 'ext'">
						<EditorExt
							v-model="localModelValue"
							:vars="props.vars"
							:language="language"
							class="h-full"
						/>
					</ComponentWait>
				</div>
			</div>
		</div>
	</teleport>
</template>

<script setup lang="ts">
	import { ref, computed } from "vue";
	// 引入组件
	import EditorCode from "@/components/utils/editor_code.vue";
	import EditorExt from "@/components/utils/editor_ext.vue";
	// 引入异步加载包装组件
	import ComponentWait from "@/components/utils/component_wait.vue";
	import { useI18n } from "vue-i18n";

	// 注入i18n
	const { t } = useI18n();

	const props = defineProps({
		modelValue: {
			type: String,
			required: true,
		},
		vars: {
			type: Object,
			default: () => ({}),
		},
		editorType: {
			type: String,
			default: "code", // 'code' 或 'ext'
			validator: (value: string) => ["code", "ext"].includes(value),
		},
		language: {
			type: String,
			default: "javascript",
		},
		highlightVariables: {
			type: Boolean,
			default: true,
		},
		editorClass: {
			type: String,
			default: "",
		},
	});

	const emit = defineEmits(["update:modelValue"]);

	const showFullscreenEditor = ref(false);

	const localModelValue = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	// 关闭函数
	function closeFullscreenEditor() {
		showFullscreenEditor.value = false;
	}

	// 打开函数
	function openFullscreenEditor() {
		showFullscreenEditor.value = true;
	}
</script>
