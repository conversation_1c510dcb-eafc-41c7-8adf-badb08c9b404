<template>
	<div class="multi-select">
		<div class="selected-options mb-3 flex flex-wrap gap-2" v-if="modelValue.length > 0">
			<div
				v-for="value in modelValue"
				:key="value"
				class="selected-option flex items-center bg-primary/10 px-3 py-1 rounded-md"
			>
				<span class="mr-2 text-sm">{{ getOptionName(value) }}</span>
				<button @click="removeOption(value)" class="text-gray-500 hover:text-error">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="h-4 w-4"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M6 18L18 6M6 6l12 12"
						/>
					</svg>
				</button>
			</div>
		</div>

		<!-- 下拉选择框 -->
		<div class="relative">
			<select
				class="select select-bordered w-full"
				v-model="selectedOption"
				@change="addOption"
				:disabled="modelValue.length >= options.length"
			>
				<option value="" disabled selected>{{ t("pls-select") }}</option>
				<option
					v-for="option in availableOptions"
					:key="option.value"
					:value="option.value"
				>
					{{ option.name }}
				</option>
			</select>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { ref, computed } from "vue";
	import type { PropType } from "vue";
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();

	const props = defineProps({
		modelValue: {
			type: Array as PropType<string[]>,
			default: () => [],
		},
		options: {
			type: Array as PropType<{ value: string; name: string }[]>,
			required: true,
		},
	});

	const emit = defineEmits(["update:modelValue"]);

	const selectedOption = ref("");

	// 计算可用选项（排除已选择的）
	const availableOptions = computed(() => {
		return props.options.filter((option) => !props.modelValue.includes(option.value));
	});

	// 添加选项
	const addOption = () => {
		if (selectedOption.value && !props.modelValue.includes(selectedOption.value)) {
			const newValue = [...props.modelValue, selectedOption.value];
			emit("update:modelValue", newValue);
			selectedOption.value = ""; // 重置选择框
		}
	};

	// 移除选项
	const removeOption = (value: string) => {
		const newValue = props.modelValue.filter((v) => v !== value);
		emit("update:modelValue", newValue);
	};

	// 获取选项名称
	const getOptionName = (value: string) => {
		const option = props.options.find((opt) => opt.value === value);
		return option ? option.name : value;
	};
</script>

<style scoped>
	.multi-select {
		width: 100%;
	}
</style>
