<template>
	<input
		type="text"
		v-model="internalName"
		class="input input-bordered w-full block"
		@blur="checkName"
	/>
</template>

<script setup lang="ts">
	import { ref, watch } from "vue";
	import { useVueFlow } from "@vue-flow/core";
	import checkNodeNameExists from "@/utils/node_name_check";
	import { Error } from "@/utils/notify";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	const props = defineProps({
		modelValue: String,
		nodeId: String,
	});

	const emits = defineEmits(["update:modelValue"]);

	const { nodes } = useVueFlow();
	const internalName = ref(props.modelValue);

	const checkName = () => {
		// 判断是否有空格，如果有去掉空格
		internalName.value = internalName.value?.trim().replace(/\s+/g, "");

		if (!internalName.value || internalName.value.trim() === "") {
			Error(t("node_name_cannot_be_empty"));
			internalName.value = props.modelValue; // 重置为原始值
			return;
		}
		if (checkNodeNameExists(nodes, internalName.value!, props.nodeId)) {
			Error(t("node_name_already_exists", { name: internalName.value }));
			internalName.value = props.modelValue; // 重置为原始值
			return;
		}
		emits("update:modelValue", internalName.value);
	};

	watch(
		() => props.modelValue,
		(newName) => {
			internalName.value = newName;
		}
	);
</script>
