<template>
	<div
		v-if="show"
		class="fixed bg-white shadow-lg rounded-lg py-0 z-50 border border-gray-100"
		:style="{ left: x + 'px', top: y + 'px' }"
	>
		<div
			v-for="item in menuItems"
			:key="item.label"
			class="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center gap-2 text-sm"
			@click="item.action"
		>
			<component :is="item.icon" class="w-4 h-4" />
			{{ item.label }}
		</div>
	</div>
</template>

<script setup lang="ts">
	interface MenuItem {
		label: string;
		icon: any;
		action: () => void;
	}

	defineProps({
		show: Boolean,
		x: Number,
		y: Number,
		menuItems: {
			type: Array as () => MenuItem[],
			default: () => [],
		},
	});
</script>
