<template>
	<div
		class="w-full h-full flex gap-2"
		:class="{
			'flex-row': prop.view_mode == 'left-right',
			'flex-col': prop.view_mode == 'default',
		}"
	>
		<div
			v-if="!hasOutputNode"
			class="alert alert-error mt-2 flex flex-nowrap rounded-md shadow-sm"
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				class="stroke-current flex-shrink-0 h-6 w-6"
				fill="none"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
				/>
			</svg>
			<span>{{ t("warnning-no-output-node") }}</span>
		</div>

		<div class="flex-1">
			<div
				v-if="Object.keys(input).length > 0"
				class="p-2 border border-gray-200 rounded-md flex flex-wrap mt-3 space-y-4 pb-4 shadow-sm"
			>
				<div
					v-for="key in Object.keys(input)"
					:key="key"
					class="px-2 flex-shrink-1 w-full"
					:class="{
						'border border-1 border-red-500 mt-1 rounded-md':
							invalidInputs.includes(key),
					}"
				>
					<label class="label">{{ key }}:</label>
					<input
						v-if="inputMeta[key].type == 'text'"
						class="input input-bordered w-full input-md"
						v-model="input[key]"
					/>
					<textarea
						v-if="inputMeta[key].type == 'longtext'"
						class="textarea textarea-bordered w-full leading-4"
						rows="10"
						v-model="input[key]"
					/>
					<select
						v-if="inputMeta[key].type == 'select'"
						class="select select-bordered w-full"
						v-model="input[key]"
						:class="{ 'select-error': invalidInputs.includes(key) }"
					>
						<option value="" disabled selected>{{ t("pls-select") }}</option>
						<option
							v-for="option in inputMeta[key].options"
							:key="option.value"
							:value="option.value"
						>
							{{ option.value }}
						</option>
					</select>
					<image-upload
						v-if="inputMeta[key].type == 'image'"
						v-model="input[key]"
						:max-count="inputMeta[key].default || 1"
						@error="(error) => handleImageError(key, error)"
						@update:modelValue="() => clearImageError(key)"
					/>
					<div
						v-if="inputMeta[key].type == 'radio'"
						class="flex flex-wrap gap-2 rounded-md"
					>
						<radio-input v-model="input[key]" :options="inputMeta[key].options" />
					</div>
					<div
						v-if="inputMeta[key].type == 'checkbox'"
						class="flex flex-wrap gap-2 rounded-md"
					>
						<checkbox-input
							v-model="input[key]"
							:options="inputMeta[key].options"
						/>
					</div>
					<p
						v-if="invalidInputs.includes(key)"
						class="text-red-500 text-sm my-2 flex items-center"
					>
						{{ getErrorMessage(key) }}
					</p>
				</div>
			</div>
			<div class="text-center mt-3">
				<button
					class="btn btn-gradient-primary mt-3 w-1/2 transition-all"
					:class="{
						'btn-disabled': isRunning || !hasOutputNode,
					}"
					@click="start"
				>
					<RocketLaunchIcon class="w-4 h-4 mr-1" />
					{{ t("start-exec") }}
				</button>
			</div>
			<div class="log-container relative overflow-hidden mt-3">
				<div
					ref="logRef"
					class="mt-2 bg-slate-700 rounded-lg text-white text-xs overflow-y-auto transition-all duration-300"
					:class="{
						'h-[130px] p-3': isLogExpanded,
						'h-0 px-3': !isLogExpanded,
					}"
					:style="{
						opacity: isLogExpanded ? 1 : 0,
					}"
				>
					<div
						v-for="(logItem, index) in logItems"
						:key="index"
						class="fade-in-item"
						:style="{
							marginLeft: (logItem.deep || 0) * 20 + 'px',
						}"
					>
						<template v-if="logItem.type === 'node'">
							'{{ logItem.name }}' {{ logItem.action }}
							<template v-if="logItem.duration"
								>{{ t("duration") }}: {{ logItem.duration }}
								{{ t("seconds") }}</template
							>
						</template>
						<span v-else-if="logItem.type === 'error'" class="text-red-400">
							{{ t("error") }}: '{{ logItem.message }}'
						</span>
						<div
							v-else-if="logItem.type === 'agent'"
							class="whitespace-pre-wrap p-2 rounded-md border-l-2 my-1"
						>
							<div class="flex items-center mb-1 text-xs">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-3 w-3 mr-1"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M13 10V3L4 14h7v7l9-11h-7z"
									/>
								</svg>
								{{ t("agent") }}
							</div>
							<div class="text-xs">{{ logItem.content }}</div>
						</div>
						<div v-else-if="logItem.type === 'stream'" class="whitespace-pre-wrap">
							<template v-if="logItem.reasoning">
								<div
									class="border-l-2 border-emerald-400 pl-3 my-2 opacity-90"
								>
									<div
										class="flex items-center text-emerald-400 mb-1 text-xs"
									>
										<svg
											xmlns="http://www.w3.org/2000/svg"
											class="h-4 w-4 mr-1"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
											/>
										</svg>
										<span>{{ t("ai-reasoning") }}</span>
									</div>
									<div class="text-gray-300 text-xs leading-relaxed">
										<!-- Consolidated reasoning content -->
										<span v-if="logItem.consolidatedReasoning">
											{{ logItem.consolidatedReasoning }}</span
										>
										<!-- New reasoning characters with animation -->
										<span
											v-for="(
												char, charIndex
											) in logItem.pendingReasoningChars"
											:key="'reasoning-pending-' + charIndex"
											class="fade-in-char"
										>
											{{ char }}
										</span>
									</div>
								</div>
							</template>
							<!-- Consolidated content -->
							<span v-if="logItem.consolidatedContent">
								{{ logItem.consolidatedContent }}</span
							>
							<!-- New characters with animation -->
							<span
								v-for="(char, charIndex) in logItem.pendingChars"
								:key="'pending-' + charIndex"
								class="fade-in-char"
							>
								{{ char }}
							</span>
						</div>
					</div>
				</div>

				<div v-if="logItems.length > 0" class="text-center h-6 mt-2">
					<button @click="toggleLogExpand" class="btn btn-xs btn-ghost">
						{{ isLogExpanded ? t("collapse") : t("expand") }}
					</button>
				</div>
			</div>
		</div>
		<div class="flex-1">
			<!-- 修改后的分隔线部分 -->
			<div class="divider text-base-300 text-xs">
				<div class="flex items-center justify-between">
					<div>
						{{ t("output") }}
						<span v-if="running_time"
							>{{ t("running_time") }}: {{ running_time.toFixed(2) }}
							{{ t("seconds") }}</span
						>
					</div>
					<button
						v-if="output"
						@click="copyOutput"
						class="ml-2 p-1 hover:bg-gray-100 rounded-md text-base-500 hover:text-gray-700 transition-colors"
						:title="t('copy')"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-4 w-4"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
							/>
						</svg>
					</button>
				</div>
			</div>

			<div class="mt-3 rounded-lg p-3">
				<component_wait>
					<template v-if="output">
						<viewer class="w-full h-full" :output="output"></viewer>
					</template>
					<template v-else>
						<div
							class="flex flex-col items-center justify-center py-8 text-center"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								class="h-12 w-12 text-gray-300 mb-4"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="1.5"
									d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
								/>
							</svg>
							<p class="text-gray-500 text-base mb-1">
								{{ t("no_results_yet") }}
							</p>
							<p class="text-gray-400 text-sm">{{ t("click_start_to_run") }}</p>
						</div>
					</template>
				</component_wait>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { nextTick, onDeactivated, ref, watch, defineAsyncComponent } from "vue";
	import type { Node } from "@vue-flow/core";
	import { Error, Success } from "@/utils/notify";
	import { ProjectRuntime, ProjectDebug } from "@/api/runtime";
	import { RocketLaunchIcon } from "@heroicons/vue/24/outline";
	import component_wait from "@/components/utils/component_wait.vue";
	import ImageUpload from "@/components/runtime/image_upload.vue";
	import CheckboxInput from "@/components/runtime/checkbox_input.vue";
	import RadioInput from "@/components/runtime/radio_input.vue";
	import {
		parseWorkflowInput,
		validateInputs as validateInputsUtil,
		processImages as processImagesUtil,
		prepareInputForAPI,
	} from "@/utils/input_processor";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const viewer = defineAsyncComponent(() => import("@/components/viewer.vue"));
	const prop = defineProps({
		data: {
			type: Object,
			default: () => ({}),
			required: false,
		},
		mode: {
			default: "debug",
			type: String,
			required: false,
		},
		project_id: {
			type: String,
			default: () => "",
			required: false,
		},
		view_mode: {
			type: String,
			default: () => "default", // default, left-right
			required: false,
		},
	});
	const output = ref("");
	const isRunning = ref(false);
	const input = ref<Record<string, any>>({});
	const inputMeta = ref<Record<string, any>>({});
	const logRef = ref<HTMLElement | null>(null);
	const logGetError = ref(false);
	const nodeMap = ref<Record<string, nodeNameMapValue>>({});
	let controller: AbortController | null = null;
	let currData = {};
	let running_time = ref<null | number>(null);
	const isLogExpanded = ref(false);
	const nodeStartTimes: Record<string, number> = {};
	const invalidInputs = ref<string[]>([]);
	const errorMessages = ref<Record<string, string>>({});

	interface LogItem {
		type: "node" | "error" | "stream" | "agent";
		content?: string;
		name?: string;
		action?: string;
		duration?: string;
		message?: string;
		deep?: number;
		reasoning?: string;
		// For optimized rendering
		consolidatedContent?: string;
		pendingChars?: string;
		lastConsolidationTime?: number;
		// For reasoning optimization
		consolidatedReasoning?: string;
		pendingReasoningChars?: string;
	}

	const logItems = ref<LogItem[]>([]);

	const onEvent = (event: string, data: Record<string, any>) => {
		if (event == "output") {
			output.value = data["output"] || "" + "";
			running_time.value = data["total_time"] || null;
		}
		if (event == "msg") {
			let name = nodeMap.value[data["Node"]] || { name: "", deep: 0 };
			if (data["Type"] == "start_node") {
				logItems.value.push({
					type: "node",
					name: name.name,
					action: t("start-exec"),
					deep: name.deep,
				});
				data["Node"] && (nodeStartTimes[data["Node"]] = Date.now());
			}
			if (data["Type"] == "finish_node") {
				let duration = 0;
				if (data["Node"] && nodeStartTimes[data["Node"]]) {
					duration = (Date.now() - nodeStartTimes[data["Node"]]) / 1000;
					delete nodeStartTimes[data["Node"]];
				}
				logItems.value.push({
					type: "node",
					name: name.name,
					action: t("stop-exec"),
					duration: duration.toFixed(2),
					deep: name.deep,
				});
			}
		}
		if (event == "error") {
			logItems.value.push({ type: "error", message: data["error"] });
			logGetError.value = true;
			isLogExpanded.value = true;
		}
		if (event == "agent") {
			logItems.value.push({ type: "agent", content: data["Content"] });
		}
		if (event == "stream") {
			const lastItem = logItems.value[logItems.value.length - 1];
			if (lastItem && lastItem.type === "stream") {
				// Update content
				const newContent = data["Content"] || "";
				lastItem.content = (lastItem.content || "") + newContent;
				lastItem.pendingChars = (lastItem.pendingChars || "") + newContent;

				// Update reasoning if present
				if (data["Reasoning"]) {
					const newReasoning = data["Reasoning"];
					lastItem.reasoning = (lastItem.reasoning || "") + newReasoning;
					lastItem.pendingReasoningChars =
						(lastItem.pendingReasoningChars || "") + newReasoning;
				}

				// Consolidate older content periodically to improve performance
				consolidateContent(lastItem);
			} else {
				// Create new stream item
				const newContent = data["Content"] || "";
				const newReasoning = data["Reasoning"];
				logItems.value.push({
					type: "stream",
					content: newContent,
					reasoning: newReasoning,
					pendingChars: newContent,
					pendingReasoningChars: newReasoning,
					consolidatedContent: "",
					consolidatedReasoning: "",
					lastConsolidationTime: Date.now(),
				});
			}
			// 触发重新渲染
			logItems.value = [...logItems.value];
		}
		nextTick(() => {
			if (logRef.value) {
				logRef.value.scrollTo({
					top: logRef.value.scrollHeight,
					behavior: "smooth",
				});
			}
		});
	};

	const validateInputs = () => {
		const validation = validateInputsUtil(input.value, inputMeta.value, t);
		invalidInputs.value = validation.invalidInputs;
		errorMessages.value = validation.errorMessages;
		return validation.isValid;
	};

	const processImages = async () => {
		// 进度回调函数
		const onProgress = (uploaded: number, total: number) => {
			// 更新上传进度
			logItems.value.push({
				type: "node",
				name: t("image-upload-progress"),
				action: t("uploaded-image-progress", {
					uploaded,
					total,
				}),
			});
		};

		// 使用公共模块处理图片
		const processedInput = await processImagesUtil(
			input.value,
			inputMeta.value,
			t,
			onProgress
		);
		// 更新本地输入值
		for (const [key, value] of Object.entries(processedInput)) {
			if (input.value[key] !== value) {
				input.value[key] = value;
			}
		}
		return processedInput;
	};

	const start = async () => {
		if (!hasOutputNode.value) {
			return;
		}
		if (!validateInputs()) {
			return;
		}

		try {
			isRunning.value = true;
			// 添加上传状态显示
			logItems.value.push({
				type: "node",
				name: t("image-upload-status"),
				action: t("start-upload"),
			});
			isLogExpanded.value = true;

			const processedInput = await processImages();

			// 上传完成提示
			logItems.value.push({
				type: "node",
				name: t("image-upload-status"),
				action: t("upload-success"),
			});

			if (prop.mode == "debug") {
				await debug(processedInput);
			}
			if (prop.mode == "run") {
				await run(processedInput);
			}
		} catch (error) {
			Error(t("error"), t("img_upload_err") + error);
			console.error(error);
			// 添加上传失败的日志
			logItems.value.push({ type: "error", message: t("img_upload_err") + error });
			isRunning.value = false;
		}
	};

	const debug = async (processedInput: Record<string, any>) => {
		let obj = currData || {};
		isLogExpanded.value = true;
		output.value = "";
		running_time.value = null;
		logItems.value = [];
		logGetError.value = false;

		// 使用公共模块准备API输入
		const inputToSend = prepareInputForAPI(processedInput, inputMeta.value);

		try {
			await ProjectDebug(inputToSend, JSON.stringify(obj), onEvent);
		} catch (error) {
			Error(t("error"), t("debug_err") + error);
		} finally {
			handleRunComplete();
		}
	};

	const run = async (processedInput: Record<string, any>) => {
		isLogExpanded.value = true;
		output.value = "";
		running_time.value = null;
		logItems.value = [];
		logGetError.value = false;

		// 使用公共模块准备API输入
		const inputToSend = prepareInputForAPI(processedInput, inputMeta.value);

		try {
			await ProjectRuntime(prop.project_id, inputToSend, onEvent);
		} catch (error) {
			Error(t("error"), t("run_err") + error);
		} finally {
			handleRunComplete();
		}
	};

	const getErrorMessage = (key: string) => {
		return errorMessages.value[key] || t("error");
	};

	const hasOutputNode = ref(false);

	type nodeNameMapValue = {
		name: string;
		deep: number;
	};
	const getNodeNameMap = (data: any, deep: number = 0) => {
		let nodes = data.nodes || [];
		let nodeNameMap: Record<string, nodeNameMapValue> = {};
		nodes.forEach((i: Node) => {
			nodeNameMap[i.id] = { name: i.data.name, deep: deep };
			if (i.data?.input?.workflow) {
				nodeNameMap = {
					...nodeNameMap,
					...getNodeNameMap(i.data.input.workflow, deep + 1),
				};
			}
		});
		return nodeNameMap;
	};
	const init = (data: any) => {
		// 使用公共模块解析工作流输入
		const { inputMeta: metaData, inputValues } = parseWorkflowInput(data);
		input.value = inputValues;
		inputMeta.value = metaData;

		// 检查是否有输出节点
		hasOutputNode.value = false;
		const nodes = data.nodes || [];
		nodes.forEach((i: Node) => {
			if (i.type == "out") {
				hasOutputNode.value = true;
			}
		});

		nodeMap.value = getNodeNameMap(data, 0);
		currData = data;
	};

	watch(
		() => prop.data,
		(data) => {
			init(data);
		}
	);

	onDeactivated(() => {
		if (controller) {
			// @ts-ignore
			controller.abort();
		}
	});

	init(prop.data);

	const toggleLogExpand = () => {
		isLogExpanded.value = !isLogExpanded.value;
		if (isLogExpanded.value && logRef.value) {
			nextTick(() => {
				if (logRef.value) {
					logRef.value.scrollTop = logRef.value.scrollHeight;
				}
			});
		}
	};

	// Function to consolidate content to improve performance
	const consolidateContent = (item: LogItem) => {
		const now = Date.now();
		const CONSOLIDATION_INTERVAL = 500; // Consolidate every 500ms
		const MAX_PENDING_CHARS = 100; // Maximum number of characters to keep as individual spans

		// Check if it's time to consolidate
		if (
			!item.lastConsolidationTime ||
			now - item.lastConsolidationTime < CONSOLIDATION_INTERVAL
		) {
			return;
		}

		// Consolidate content if there are enough pending characters
		if (item.pendingChars && item.pendingChars.length > MAX_PENDING_CHARS) {
			// Keep the last MAX_PENDING_CHARS characters as pending, consolidate the rest
			const charsToConsolidate = item.pendingChars.slice(0, -MAX_PENDING_CHARS);
			item.consolidatedContent = (item.consolidatedContent || "") + charsToConsolidate;
			item.pendingChars = item.pendingChars.slice(-MAX_PENDING_CHARS);
		}

		// Consolidate reasoning if present
		if (
			item.pendingReasoningChars &&
			item.pendingReasoningChars.length > MAX_PENDING_CHARS
		) {
			const reasoningToConsolidate = item.pendingReasoningChars.slice(
				0,
				-MAX_PENDING_CHARS
			);
			item.consolidatedReasoning =
				(item.consolidatedReasoning || "") + reasoningToConsolidate;
			item.pendingReasoningChars = item.pendingReasoningChars.slice(-MAX_PENDING_CHARS);
		}

		// Update consolidation timestamp
		item.lastConsolidationTime = now;
	};

	// Consolidate all content when run completes
	const consolidateAllContent = () => {
		logItems.value.forEach((item) => {
			if (item.type === "stream") {
				// Consolidate all pending content
				if (item.pendingChars && item.pendingChars.length > 0) {
					item.consolidatedContent =
						(item.consolidatedContent || "") + item.pendingChars;
					item.pendingChars = "";
				}

				// Consolidate all pending reasoning
				if (item.pendingReasoningChars && item.pendingReasoningChars.length > 0) {
					item.consolidatedReasoning =
						(item.consolidatedReasoning || "") + item.pendingReasoningChars;
					item.pendingReasoningChars = "";
				}
			}
		});
	};

	const handleRunComplete = () => {
		isRunning.value = false;
		if (!logGetError.value) {
			isLogExpanded.value = false;
		}
		// Consolidate all content when run completes for maximum performance
		consolidateAllContent();
	};

	const handleImageError = (key: string, error: string) => {
		invalidInputs.value.push(key);
		errorMessages.value[key] = error;
	};

	const clearImageError = (key: string) => {
		invalidInputs.value = invalidInputs.value.filter((k) => k !== key);
		delete errorMessages.value[key];
	};
	const copyOutput = () => {
		if (output.value) {
			navigator.clipboard.writeText(output.value);
			Success(t("copy_success"), t("copied_to_clipboard"));
		}
	};
</script>

<style scoped>
	.fade-in-item {
		animation: fadeIn 0.5s ease-in-out forwards;
	}

	.fade-in-char {
		opacity: 0;
		animation: fadeIn 0.3s ease-in-out forwards;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	.text-red-500 {
		@apply bg-red-50 border border-red-200 rounded-md px-2 py-1 transition-all duration-300 ease-in-out;
	}

	/* 新增样式 */
	.bg-blue-50 {
		@apply transition-all duration-300 ease-in-out;
	}

	.bg-blue-50:hover {
		@apply bg-blue-100;
	}

	.text-blue-700 {
		@apply leading-relaxed;
	}
</style>
