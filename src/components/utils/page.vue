<template>
	<div class="flex items-center justify-center space-x-2">
		<button
			v-for="pageNumber in pages"
			:key="pageNumber"
			:class="[
				'btn btn-square btn-sm text-sm',
				{ 'btn-disabled': pageNumber === '...' || pageNumber === currentPage },
			]"
			:aria-label="pageNumber.toString()"
			@click="handlePageChange(pageNumber)"
		>
			{{ pageNumber }}
		</button>
	</div>
</template>

<script lang="ts">
	import { defineComponent, computed, watch, ref } from "vue";

	export default defineComponent({
		props: {
			page: {
				type: Number,
				required: true,
			},
			pageSize: {
				type: Number,
				required: true,
			},
			total: {
				type: Number,
				required: true,
			},
		},
		emits: ["change"],
		setup(props, { emit }) {
			const currentPage = ref(props.page);

			const totalPages = computed(() => Math.ceil(props.total / props.pageSize));

			const pages = computed(() => {
				const pagesArray = [];
				const maxVisiblePages = 5;
				let startPage = Math.max(1, currentPage.value - 2);
				let endPage = Math.min(totalPages.value, startPage + maxVisiblePages - 1);

				if (endPage - startPage + 1 < maxVisiblePages) {
					startPage = Math.max(1, endPage - maxVisiblePages + 1);
				}

				for (let i = startPage; i <= endPage; i++) {
					pagesArray.push(i);
				}

				if (startPage > 1) {
					pagesArray.unshift("...");
				}
				if (endPage < totalPages.value) {
					pagesArray.push("...");
				}

				return pagesArray;
			});

			watch(
				() => props.page,
				(newPage) => {
					currentPage.value = newPage;
				}
			);

			const handlePageChange = (pageNumber: number | string) => {
				if (pageNumber !== "..." && pageNumber !== currentPage.value) {
					emit("change", pageNumber);
				}
			};

			return { pages, currentPage, handlePageChange };
		},
	});
</script>

<style scoped>
	.btn-disabled {
		cursor: not-allowed;
		opacity: 0.5;
	}
</style>
