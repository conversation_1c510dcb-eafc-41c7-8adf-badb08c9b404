<template>
	<div class="myinput relative hover:bg-gray-200">
		<slot name="body" :data="data" />
		<Handle type="target" :position="Position.Left" />
		<Add @showOptions="showOptions"></Add>
	</div>
</template>
<script setup lang="ts">
	import { Handle, Position, useNode } from "@vue-flow/core";
	import Add from "@/components/utils/add.vue";

	const emit = defineEmits(["showOptions"]);
	const showOptions = (id: any, data: any, e: MouseEvent) => {
		emit("showOptions", id, data, e);
	};

	const node = useNode();
	const data = node.node.data;
</script>
<style scoped>
	.myinput {
		font-weight: bold;
		display: flex;
		padding: 10px;
		font-size: 13px;
		border-radius: 15px;
		width: 200px;
		/* min-height: 60px; */
		max-width: 300px;
		background-color: #fff;
		box-shadow: 1px 4px 7px 0px rgba(0, 0, 0, 0.1);
		transition: 0.3s;
		border: 1px solid #f8f8f8;
		flex-direction: column;
		justify-content: start;
		align-items: start;
	}
	.myinput:hover {
		background-color: #f8f8f8;
		@apply shadow-xl;
	}
</style>
