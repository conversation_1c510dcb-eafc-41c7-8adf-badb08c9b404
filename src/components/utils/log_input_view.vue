<script setup lang="ts">
	import { ref, computed } from "vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const props = defineProps({
		logInput: {
			type: String,
			required: true,
		},
	});

	const isJson = computed(() => {
		try {
			JSON.parse(props.logInput);
			return true;
		} catch (e) {
			return false;
		}
	});

	const parsedJson = computed(() => {
		if (isJson.value) {
			return JSON.parse(props.logInput);
		}
		return null;
	});

	const getObjectEntries = (obj: Object) => {
		return Object.entries(obj).map(([key, value]) => ({
			key,
			value: typeof value === "object" ? JSON.stringify(value) : value,
		}));
	};

	const truncatedValues = ref<Record<string, boolean>>({});

	const toggleTruncate = (key: string) => {
		truncatedValues.value[key] = !truncatedValues.value[key];
	};

	const isImageUrls = (value: any): boolean => {
		try {
			const parsed = JSON.parse(value);
			if (!Array.isArray(parsed)) return false;

			return parsed.every((url) => {
				if (typeof url !== "string") return false;
				return url.match(
					/^https:\/\/p\d+\.storage\.flowai\.cc\/uploads\/\d{4}\/\d{2}\/\d{2}\/[\w-]+\.(png|jpg|jpeg|gif)$/i
				);
			});
		} catch {
			return false;
		}
	};

	const getImageUrls = (value: string): string[] => {
		try {
			return JSON.parse(value);
		} catch {
			return [];
		}
	};

	const getDisplayValue = (value: string, key: string) => {
		if (isImageUrls(value)) {
			return value;
		}
		if (value.length > 100 && !truncatedValues.value[key]) {
			return value.slice(0, 100) + "...";
		}
		return value;
	};

	// 添加预览相关的状态
	const previewImage = ref<string | undefined>(undefined);
	const showPreview = ref(false);

	// 添加打开预览的方法
	const openPreview = (url: string) => {
		previewImage.value = url;
		showPreview.value = true;
	};

	// 添加关闭预览的方法
	const closePreview = () => {
		showPreview.value = false;
		previewImage.value = undefined;
	};
</script>

<template>
	<div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200">
		<div v-if="isJson" class="space-y-3">
			<div
				v-for="(item, index) in getObjectEntries(parsedJson)"
				:key="index"
				v-if="getObjectEntries(parsedJson).length > 0"
				class="group"
			>
				<div class="flex items-center space-x-2 mb-1">
					<span class="font-medium text-blue-600">{{ item.key }}:</span>
					<span
						class="text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity"
					>
						({{
							(() => {
								try {
									return typeof JSON.parse(item.value);
								} catch {
									return typeof item.value;
								}
							})()
						}})
					</span>
				</div>
				<div
					class="bg-white p-3 rounded-md shadow-sm border border-gray-100 hover:border-blue-200 transition-colors"
				>
					<template v-if="isImageUrls(item.value)">
						<div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
							<div
								v-for="(url, imgIndex) in getImageUrls(item.value)"
								:key="imgIndex"
								class="relative w-full h-32 group cursor-pointer after:content-[''] after:rounded-md after:absolute after:inset-0 after:bg-black/50 after:opacity-0 after:transition-opacity after:duration-200 hover:after:opacity-50"
								@click="openPreview(url)"
							>
								<img
									:src="url"
									class="absolute inset-0 w-full h-full object-contain rounded-lg transition-all duration-200 border"
									:alt="`Image ${imgIndex + 1}`"
								/>
							</div>
						</div>

						<!-- 图片预览模态框 -->
						<div
							v-if="showPreview"
							class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
							@click="closePreview"
						>
							<div class="relative max-w-4xl max-h-[90vh] mx-4">
								<img
									:src="previewImage"
									class="max-w-full max-h-[90vh] object-contain"
									alt="Preview"
								/>
								<button
									class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75"
									@click.stop="closePreview"
								>
									<svg
										class="w-6 h-6"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M6 18L18 6M6 6l12 12"
										></path>
									</svg>
								</button>
							</div>
						</div>
					</template>
					<template v-else>
						<pre class="text-gray-700 text-sm break-all whitespace-pre-wrap">{{
							getDisplayValue(item.value, item.key)
						}}</pre>
						<button
							v-if="item.value.length > 100"
							@click="toggleTruncate(item.key)"
							class="text-blue-500 text-xs mt-1 hover:text-blue-700 focus:outline-none"
						>
							{{ truncatedValues[item.key] ? t("collapse") : t("expand") }}
						</button>
					</template>
				</div>
			</div>
			<div v-else class="text-center py-4">
				<span class="text-gray-400">{{ t("no_input_data") }}</span>
			</div>
		</div>
		<div v-else class="bg-white p-4 rounded-md shadow-sm border border-gray-100">
			<div class="relative">
				<pre class="whitespace-pre-wrap text-gray-700 text-sm break-all">{{
					logInput
				}}</pre>
				<div
					v-if="logInput.length > 100"
					class="absolute bottom-0 right-0 bg-gradient-to-t from-white via-white to-transparent h-12 w-full pointer-events-none"
				></div>
			</div>
		</div>
	</div>
</template>
