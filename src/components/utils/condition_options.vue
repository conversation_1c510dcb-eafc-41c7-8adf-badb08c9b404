<template>
	<div class="flex flex-col">
		<div class="flex items-center space-x-2">
			<select
				v-model="option.condition_type"
				class="select select-bordered select-xs w-20 flex-grow-[1] flex-shrink-[1]"
			>
				<option v-for="(label, type) in conditionTypes(t)" :key="type" :value="type">
					{{ label }}
				</option>
			</select>
			<input
				v-model="option.value"
				type="text"
				:placeholder="t('input_value')"
				class="input input-bordered input-xs min-w-20 flex-grow-[4] flex-shrink-[4]"
			/>
			<button @click="$emit('delete')" class="btn btn-xs ml-2 flex-shrink-0 flex-grow-0">
				{{ t("delete") }}
			</button>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { defineProps, defineEmits, computed } from "vue";
	import { ConditionType, conditionTypes } from "@/utils/condition_types";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	const props = defineProps({
		modelValue: {
			type: Object,
			required: true,
		},
	});

	const emit = defineEmits(["update:modelValue", "delete"]);

	const option = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});
</script>
