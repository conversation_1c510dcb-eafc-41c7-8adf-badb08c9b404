<template>
	<div class="custom-list-container">
		<MdPreview
			editorId="preview-only"
			:modelValue="props.output"
			previewTheme="github"
			:preview="true"
			style="background: none"
			:codeFoldable="false"
		></MdPreview>
	</div>
</template>
<script setup lang="ts">
	import { MdPreview, config } from "md-editor-v3";
	import { TargetBlankExtension } from "@/utils/runtime";
	import "md-editor-v3/lib/preview.css";

	config({
		markdownItConfig(md) {
			md.use(TargetBlankExtension);
		},
	});

	const props = defineProps({
		output: String,
	});
</script>

<style scoped>
	.custom-list-container :deep(ol) {
		list-style-type: none;
		counter-reset: item;
	}

	.custom-list-container :deep(ol > li) {
		counter-increment: item;
		position: relative;
	}

	.custom-list-container :deep(ol > li::before) {
		content: counter(item) ".";
		position: absolute;
		left: -1.5em;
		/* width: 12em; */
		text-align: start;
		color: #666;
	}

	.custom-list-container :deep(ul) {
		list-style-type: none;
	}

	.custom-list-container :deep(ul > li::before) {
		content: "•";
		color: #666;
		display: inline-block;
		width: 0.8em;
		text-align: start;
		margin-left: -1em;
	}

	/* 新增：处理任务列表 */
	.custom-list-container :deep(ul.contains-task-list > li::before) {
		content: none;
	}

	.custom-list-container :deep(.md-editor-preview-wrapper) {
		padding: 10px 10px;
	}
</style>
