<template>
	<div>
		<teleport to="body">
			<div class="fixed inset-0 bg-black bg-opacity-0" @click="emitClose">
				<div
					ref="focusableDiv"
					tabindex="0"
					class="w-full h-full"
					@keydown.prevent="disableDelPropagation"
					autofocus
				></div>
			</div>
		</teleport>

		<teleport to="body">
			<div
				v-motion-slide-visible-right
				class="fixed flex items-center right-0 top-0 h-full w-full md:w-[420px]"
				:style="{ width: isMobile ? '100%' : width + 'px' }"
				ref="container"
			>
				<div
					v-if="!isMobile"
					class="absolute z-50 left-0 top-1/2 -translate-y-1/2 w-1 h-20 cursor-ew-resize bg-black bg-opacity-10 hover:bg-opacity-20 hidden md:block"
					@mousedown="startResize"
				></div>
				<div
					class="card box-border bg-base-100 shadow-xl w-full p-4 md:p-8 h-full md:h-[80%] overflow-y-auto overflow-x-hidden relative"
					@keydown="disableDelPropagation"
				>
					<button
						class="absolute top-2 right-2 md:right-5 btn btn-sm btn-circle font-bold text-sm"
						@click="emitClose"
					>
						✕
					</button>
					<h2 class="card-title">
						{{ t("node-config") }}
						<a
							v-if="nodeConfig?.helpDoc"
							class="ml-2 text-sm text-gray-500 hover:text-gray-700 cursor-pointer flex items-center gap-1 self-center transition-all duration-300"
							@click="openHelp"
						>
							<QuestionMarkCircleIcon class="w-4 h-4" />
							{{ t("help-doc") }}
						</a>
					</h2>

					<div class="divider"></div>
					<div class="card_body" v-if="nodeConfig && nodeConfig.cfgRender">
						<component_wait>
							<component
								:is="nodeConfig.cfgRender"
								:id="prop.id"
								:flowId="prop.flowId"
							></component>
						</component_wait>
					</div>
					<div class="card_body" v-else>{{ t("none") }}</div>
					<div class="divider mt-4"></div>
					<button
						class="btn btn-sm btn-outline btn-error text-white"
						@click="emitDelete"
					>
						{{ t("delete-node") }}
					</button>
				</div>
			</div>
		</teleport>
	</div>
</template>

<script setup lang="ts">
	import { ref, onMounted, onUnmounted, computed, provide } from "vue";
	import component_wait from "@/components/utils/component_wait.vue";
	import { GetNode } from "@/components/nodes/nodes.ts";
	import { useVueFlow } from "@vue-flow/core";
	import { QuestionMarkCircleIcon } from "@heroicons/vue/24/outline";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	const prop = defineProps<{
		id: string;
		type: string;
		flowId: string;
		subWorkflow?: any;
	}>();

	// 使用传入的 flowId
	useVueFlow(prop.flowId);

	const emit = defineEmits(["close", "delete"]);

	const container = ref<HTMLDivElement | null>(null);
	const focusableDiv = ref<HTMLDivElement | null>(null);
	const width = ref(500);
	const minWidth = 300;
	const maxWidth = window.innerWidth * 0.8;

	const isMobile = computed(() => window.innerWidth <= 768);

	const startResize = (e: MouseEvent) => {
		if (isMobile.value) return;
		e.preventDefault();
		window.addEventListener("mousemove", resize);
		window.addEventListener("mouseup", stopResize);
	};

	const resize = (e: MouseEvent) => {
		if (isMobile.value) return;
		const newWidth = window.innerWidth - e.clientX;
		width.value = Math.max(minWidth, Math.min(newWidth, maxWidth));
	};

	const stopResize = () => {
		window.removeEventListener("mousemove", resize);
		window.removeEventListener("mouseup", stopResize);
	};

	const emitClose = () => {
		emit("close");
	};

	const emitDelete = () => {
		emit("delete", prop.id);
		emitClose();
	};

	const disableDelPropagation = (event: KeyboardEvent) => {
		if (event.key === "Delete" || event.key === "Backspace") {
			event.stopPropagation(); // 阻止事件冒泡
		}
		// 添加对 Esc 键的处理
		if (event.key === "Escape") {
			emitClose();
		}
	};

	const nodeConfig = computed(() => {
		if (!prop.type) return null;
		return GetNode(prop.type, t) || null;
	});

	const openHelp = () => {
		if (!nodeConfig.value?.helpDoc) return;
		window.open(nodeConfig.value?.helpDoc, "_blank");
	};

	onMounted(() => {
		if (container.value) {
			width.value = isMobile.value ? window.innerWidth : container.value.offsetWidth;
		}
		document.body.style.overflow = "hidden"; // 防止背景滚动
		if (focusableDiv.value) {
			focusableDiv.value.focus();
		}
	});

	onUnmounted(() => {
		window.removeEventListener("mousemove", resize);
		window.removeEventListener("mouseup", stopResize);
		document.body.style.overflow = ""; // 恢复背景滚动
	});
</script>
