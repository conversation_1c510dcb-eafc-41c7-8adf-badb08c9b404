<template>
	<div class="flex flex-wrap gap-2 rounded-md">
		<label
			v-for="option in options"
			:key="option.value"
			class="flex items-center space-x-2 border border-gray-200 px-3 py-2 rounded-md shadow-sm hover:bg-gray-50 cursor-pointer"
		>
			<input
				type="radio"
				:value="option.value"
				:checked="modelValue === option.value"
				@change="emit('update:modelValue', option.value)"
				class="radio radio-primary radio-xs"
			/>
			<span class="text-sm">{{ option.value }}</span>
		</label>
	</div>
</template>

<script setup lang="ts">
	const props = defineProps<{
		modelValue: string;
		options: { value: string }[];
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: string): void;
	}>();
</script>
