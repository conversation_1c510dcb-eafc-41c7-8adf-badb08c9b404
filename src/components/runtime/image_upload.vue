<template>
	<div>
		<div class="flex flex-col gap-2">
			<div
				v-if="hasImages"
				class="flex flex-wrap gap-2 border-2 border-dashed border-gray-300 rounded-lg p-4"
			>
				<div
					v-for="(img, imgIndex) in modelValue"
					:key="imgIndex"
					class="relative w-48 h-48"
				>
					<img
						:src="img"
						class="w-full h-full rounded-lg object-cover"
						:alt="t('preview-img')"
					/>
					<div class="absolute top-2 right-2 flex gap-2">
						<button
							class="btn btn-circle btn-sm btn-error"
							@click="removeImage(imgIndex)"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								class="h-4 w-4"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M6 18L18 6M6 6l12 12"
								/>
							</svg>
						</button>
					</div>
				</div>
			</div>
			<div
				class="border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-primary transition-colors relative"
				:class="{ 'border-primary bg-primary/5': isDragging }"
				@dragenter.prevent="handleDragEnter"
				@dragleave.prevent="handleDragLeave"
				@dragover.prevent
				@drop.prevent="handleDrop"
				v-if="!hasImages || canUploadMore"
			>
				<div class="text-center" v-if="!hasImages">
					<div class="flex flex-col items-center gap-2">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-8 w-8 text-gray-400"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
							/>
						</svg>
						<div class="text-sm text-gray-600">
							{{ t("drag-image-here") }}
							<label
								class="text-primary hover:text-primary-focus cursor-pointer"
							>
								{{ t("click-upload") }}
								<input
									type="file"
									accept="image/*"
									class="hidden"
									@change="handleImageUpload"
									multiple
								/>
							</label>
						</div>
						<div class="text-xs text-gray-400">
							{{ t("support-jpg-png-gif-format") }}
							<template v-if="maxCount > 1">
								，{{ t("max-upload-image", { count: maxCount }) }}
							</template>
						</div>
					</div>
				</div>
				<div v-else-if="canUploadMore" class="text-center">
					<div class="text-sm text-gray-600">
						<label class="text-primary hover:text-primary-focus cursor-pointer">
							{{ t("continue-upload") }}
							<input
								type="file"
								accept="image/*"
								class="hidden"
								@change="handleImageUpload"
								multiple
							/>
						</label>
						<span class="text-xs text-gray-400">
							{{ t("remaining-upload-count", { count: remainingUploadCount }) }}
						</span>
					</div>
				</div>
			</div>
			<p v-if="uploadError" class="text-error text-sm mt-1">
				{{ uploadError }}
			</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { ref, computed, onBeforeUnmount } from "vue";
	import type { PropType } from "vue";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	const props = defineProps({
		modelValue: {
			type: Array as PropType<string[]>,
			default: () => [],
		},
		maxCount: {
			type: Number,
			default: 1,
		},
	});

	const emit = defineEmits(["update:modelValue", "error"]);

	const isDragging = ref(false);
	const uploadError = ref("");

	const validateImageFile = (file: File): string | null => {
		const maxSize = 5 * 1024 * 1024; // 5MB
		const allowedTypes = ["image/jpeg", "image/png", "image/gif"];

		if (!allowedTypes.includes(file.type)) {
			return t("upload-invalid-image-format");
		}

		if (file.size > maxSize) {
			return t("image-size-exceeds-limit");
		}

		return null;
	};

	const processImageFile = async (file: File) => {
		const error = validateImageFile(file);
		if (error) {
			uploadError.value = error;
			emit("error", error);
			return;
		}

		uploadError.value = "";

		try {
			const blobUrl = URL.createObjectURL(file);
			const currentValue = Array.isArray(props.modelValue) ? props.modelValue : [];
			emit("update:modelValue", [...currentValue, blobUrl]);
		} catch (error) {
			const errorMsg = t("image-processing-failed");
			uploadError.value = errorMsg;
			emit("error", errorMsg);
		}
	};

	const cleanup = () => {
		if (Array.isArray(props.modelValue)) {
			props.modelValue.forEach((url) => {
				if (typeof url === "string" && url.startsWith("blob:")) {
					URL.revokeObjectURL(url);
				}
			});
		}
	};

	onBeforeUnmount(() => {
		cleanup();
	});

	const handleDragEnter = () => {
		isDragging.value = true;
	};

	const handleDragLeave = () => {
		isDragging.value = false;
	};

	const handleDrop = async (event: DragEvent) => {
		isDragging.value = false;
		const files = event.dataTransfer?.files;
		if (files) {
			const currentCount = Array.isArray(props.modelValue)
				? props.modelValue.length
				: props.modelValue
				? 1
				: 0;
			const remainingCount = props.maxCount - currentCount;

			for (let i = 0; i < Math.min(files.length, remainingCount); i++) {
				await processImageFile(files[i]);
			}
		}
	};

	const removeImage = (index: number) => {
		const urlToRemove = props.modelValue[index] as string;
		if (urlToRemove.startsWith("blob:")) {
			URL.revokeObjectURL(urlToRemove);
		}
		const newValue = [...props.modelValue];
		newValue.splice(index, 1);
		emit("update:modelValue", newValue);
		uploadError.value = "";
	};

	const handleImageUpload = async (event: Event) => {
		const files = (event.target as HTMLInputElement).files;
		if (files) {
			const remainingCount = props.maxCount - props.modelValue.length;
			for (let i = 0; i < Math.min(files.length, remainingCount); i++) {
				await processImageFile(files[i]);
			}
		}
		// 重置input，使得同一文件可以重复上传
		(event.target as HTMLInputElement).value = "";
	};

	const hasImages = computed(() => props.modelValue.length > 0);

	const canUploadMore = computed(() => props.modelValue.length < props.maxCount);

	const remainingUploadCount = computed(() => props.maxCount - props.modelValue.length);
</script>

<style scoped>
	.btn-circle {
		@apply rounded-full w-8 h-8 min-h-0;
	}
</style>
