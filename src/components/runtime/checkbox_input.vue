<template>
	<div class="flex flex-wrap gap-2 rounded-md">
		<label
			v-for="option in options"
			:key="option.value"
			class="flex items-center space-x-2 border border-gray-200 px-3 py-2 rounded-md shadow-sm hover:bg-gray-50 cursor-pointer"
		>
			<input
				type="checkbox"
				:value="option.value"
				:checked="modelValue.includes(option.value)"
				@change="toggleOption(option.value)"
				class="checkbox checkbox-primary checkbox-sm"
			/>
			<span class="text-sm">{{ option.value }}</span>
		</label>
	</div>
</template>

<script setup lang="ts">
	const props = defineProps<{
		modelValue: string[];
		options: { value: string }[];
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: string[]): void;
	}>();

	const toggleOption = (value: string) => {
		const newValue = [...props.modelValue];
		const index = newValue.indexOf(value);
		if (index === -1) {
			newValue.push(value);
		} else {
			newValue.splice(index, 1);
		}
		emit("update:modelValue", newValue);
	};
</script>
