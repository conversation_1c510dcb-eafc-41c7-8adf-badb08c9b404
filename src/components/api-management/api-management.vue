<script setup lang="ts">
	import { onMounted } from "vue";
	import ApiKeysManager from "./ApiKeysManager.vue";
	import WorkflowsManager from "./WorkflowsManager.vue";
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();

	// 页面加载时初始化
	onMounted(() => {
		// 在这里可以添加主组件的初始化逻辑
	});
</script>

<template>
	<div>
		<!-- API Keys 管理部分 -->
		<div class="mb-8">
			<ApiKeysManager />
		</div>

		<!-- Published Workflows 管理部分 -->
		<div>
			<WorkflowsManager />
		</div>
	</div>
</template>
