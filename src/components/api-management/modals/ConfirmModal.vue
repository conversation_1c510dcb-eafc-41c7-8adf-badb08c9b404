<script setup lang="ts">
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();

	defineProps<{
		title: string;
		message: string;
	}>();

	// 定义事件
	const emit = defineEmits<{
		confirm: [];
		cancel: [];
	}>();
</script>

<template>
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg">{{ title }}</h3>
			<div class="py-4">
				<p>{{ message }}</p>
			</div>
			<div class="modal-action">
				<button @click="emit('cancel')" class="btn">
					{{ t("cancel") }}
				</button>
				<button @click="emit('confirm')" class="btn btn-error">
					{{ t("confirm") }}
				</button>
			</div>
		</div>
		<div class="modal-backdrop" @click="emit('cancel')"></div>
	</div>
</template>
