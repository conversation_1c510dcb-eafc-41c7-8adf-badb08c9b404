<script setup lang="ts">
	import { ref, computed } from "vue";
	import { useI18n } from "vue-i18n";
	import { ClipboardIcon } from "@heroicons/vue/24/outline";
	import viewer from "@/components/viewer.vue";

	const { t } = useI18n();

	// 定义props
	const props = defineProps<{
		isLoading: boolean;
		example: {
			uuid: string;
			apiKey: string;
			inputParams: Record<string, any>;
		} | null;
		activeTab: string;
	}>();

	// 定义事件
	const emit = defineEmits<{
		close: [];
		changeTab: [tab: string];
		copyUuid: [uuid: string];
	}>();

	// 生成API请求示例文本 - Curl
	const generateCurlExample = () => {
		if (!props.example) return "";
		const inputParams =
			props.example.inputParams && Object.keys(props.example.inputParams).length > 0
				? JSON.stringify({ input: props.example.inputParams })
				: '{ "input": {} }';

		return `curl -X POST https://flowai.cc/v1/api/workflow/run/${props.example.uuid} \\
-H 'X-API-KEY: ${props.example.apiKey}' \\
-d '${inputParams}'`;
	};

	// 生成JavaScript示例代码
	const generateJavaScriptExample = () => {
		if (!props.example) return "";
		const input =
			props.example.inputParams && Object.keys(props.example.inputParams).length > 0
				? { input: props.example.inputParams }
				: { input: {} };

		return `const options = {
	method: 'POST',
	headers: {
	'Content-Type': 'application/json',
	'X-API-KEY': '${props.example.apiKey}'
	},
	body: JSON.stringify(${JSON.stringify(input).replace(/"([^"]+)":/g, "$1:")})
};

fetch('https://flowai.cc/v1/api/workflow/run/${props.example.uuid}', options)
	.then(response => response.json())
	.then(data => console.log(data))
	.catch(error => console.error(error));`;
	};

	// 生成PHP示例代码
	const generatePHPExample = () => {
		if (!props.example) return "";
		const input =
			props.example.inputParams && Object.keys(props.example.inputParams).length > 0
				? JSON.stringify({ input: props.example.inputParams })
				: '{ "input": {} }';

		return `<?php
	$url = "https://flowai.cc/v1/api/workflow/run/${props.example.uuid}";
	$options = [
	  'http' => [
		'method' => 'POST',
		'header' => "Content-Type: application/json\r\nX-API-KEY: ${props.example.apiKey}",
		'content' => '${input}'
	  ]
	];

	$context = stream_context_create($options);
	$response = file_get_contents($url, false, $context);
	$result = json_decode($response, true);
	print_r($result);
?>`;
	};

	// 生成Go示例代码
	const generateGoExample = () => {
		if (!props.example) return "";
		const input =
			props.example.inputParams && Object.keys(props.example.inputParams).length > 0
				? JSON.stringify({ input: props.example.inputParams })
				: '{ "input": {} }';

		return `package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

func main() {
	url := "https://flowai.cc/v1/api/workflow/run/${props.example.uuid}"
	payload := []byte(\`${input}\`)
	
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(payload))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-KEY", "${props.example.apiKey}")
	
	client := &http.Client{}
	resp, _ := client.Do(req)
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	fmt.Println(string(body))
}`;
	};

	// 根据当前选中的标签获取代码示例
	const currentCodeExample = computed(() => {
		let code = "";
		switch (props.activeTab) {
			case "javascript":
				code += "```js\n" + generateJavaScriptExample();
				break;
			case "php":
				code += "```php\n" + generatePHPExample();
				break;
			case "go":
				code += "```go\n" + generateGoExample();
				break;
			case "curl":
				code += "```sh\n" + generateCurlExample();
				break;
			default:
		}
		return code + "\n```";
	});
</script>

<template>
	<div class="modal modal-open">
		<div class="modal-box max-w-4xl">
			<h3 class="font-bold text-lg mb-4">{{ t("api_request_example") }}</h3>

			<!-- 加载中状态 -->
			<div v-if="isLoading" class="py-4">
				<div class="flex justify-center">
					<span class="loading loading-spinner"></span>
				</div>
				<p class="text-center mt-4">{{ t("loading") }}...</p>
			</div>

			<!-- 加载完成状态 -->
			<div v-else-if="example" class="py-4">
				<!-- 基本信息卡片 -->
				<div class="card bg-base-200 mb-4 p-4 text-sm">
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<p class="font-semibold mb-1">{{ t("workflow_id") }}</p>
							<div class="flex items-center">
								<span class="font-mono">{{ example.uuid }}</span>
								<button
									@click="emit('copyUuid', example.uuid)"
									class="btn btn-ghost btn-xs ml-2"
								>
									<ClipboardIcon class="w-3 h-3" />
								</button>
							</div>
						</div>
						<div>
							<p class="font-semibold mb-1">{{ t("endpoint") }}</p>
							<span class="font-mono"
								>https://flowai.cc/v1/api/workflow/run/{uuid}</span
							>
						</div>
					</div>
				</div>
				<div class="tabs tabs-boxed">
					<a
						class="tab"
						:class="{ 'tab-active': activeTab === 'curl' }"
						@click="emit('changeTab', 'curl')"
					>
						cURL
					</a>
					<a
						class="tab"
						:class="{ 'tab-active': activeTab === 'javascript' }"
						@click="emit('changeTab', 'javascript')"
					>
						JavaScript
					</a>
					<a
						class="tab"
						:class="{ 'tab-active': activeTab === 'php' }"
						@click="emit('changeTab', 'php')"
					>
						PHP
					</a>
					<a
						class="tab"
						:class="{ 'tab-active': activeTab === 'go' }"
						@click="emit('changeTab', 'go')"
					>
						Go
					</a>
				</div>
				<viewer class="w-full h-full" :output="currentCodeExample"></viewer>
			</div>

			<div class="modal-action">
				<button @click="emit('close')" class="btn btn-primary">
					{{ t("close") }}
				</button>
			</div>
		</div>
		<div class="modal-backdrop" @click="emit('close')"></div>
	</div>
</template>
