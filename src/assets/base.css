@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义按钮样式 - 基于 ProjectsView.vue 中的漂亮样式 */
@layer components {
	/* 渐变主要按钮 */
	.btn-gradient-primary {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border-0;
	}

	.btn-gradient-primary.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	.btn-gradient-primary.btn-lg {
		@apply px-8 py-4 text-base;
	}

	/* 渐变成功按钮 */
	.btn-gradient-success {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border-0;
	}

	.btn-gradient-success.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	/* 渐变警告按钮 */
	.btn-gradient-warning {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border-0;
	}

	.btn-gradient-warning.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	/* 渐变危险按钮 */
	.btn-gradient-danger {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border-0;
	}

	.btn-gradient-danger.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	/* 渐变紫色按钮 */
	.btn-gradient-purple {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border-0;
	}

	.btn-gradient-purple.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	/* 轮廓按钮样式 */
	.btn-outline-primary {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:border-blue-300 transition-all duration-200;
	}

	.btn-outline-primary.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	.btn-outline-success {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 hover:border-green-300 transition-all duration-200;
	}

	.btn-outline-success.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	.btn-outline-warning {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-amber-600 bg-amber-50 border border-amber-200 rounded-lg hover:bg-amber-100 hover:border-amber-300 transition-all duration-200;
	}

	.btn-outline-warning.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	.btn-outline-danger {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 hover:border-red-300 transition-all duration-200;
	}

	.btn-outline-danger.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	/* 现代化的普通按钮 */
	.btn-modern {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-slate-600 bg-slate-100 border border-slate-300 rounded-lg hover:bg-slate-200 hover:border-slate-400 transition-all duration-200;
	}

	.btn-modern.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	/* 玻璃态按钮 */
	.btn-glass {
		@apply inline-flex items-center px-6 py-3 text-sm font-medium text-slate-700 bg-white/80 backdrop-blur-sm border border-slate-200 rounded-lg hover:bg-white/90 hover:border-slate-300 shadow-lg hover:shadow-xl transition-all duration-300;
	}

	.btn-glass.btn-sm {
		@apply px-4 py-2 text-xs;
	}

	/* 禁用状态 */
	.btn-gradient-primary:disabled,
	.btn-gradient-success:disabled,
	.btn-gradient-warning:disabled,
	.btn-gradient-danger:disabled,
	.btn-gradient-purple:disabled {
		@apply opacity-50 cursor-not-allowed shadow-none;
	}

	.btn-outline-primary:disabled,
	.btn-outline-success:disabled,
	.btn-outline-warning:disabled,
	.btn-outline-danger:disabled,
	.btn-modern:disabled,
	.btn-glass:disabled {
		@apply opacity-50 cursor-not-allowed;
	}

	/* 加载状态 */
	.btn-loading {
		@apply relative;
	}

	.btn-loading::before {
		content: "";
		@apply absolute inset-0 bg-current opacity-20 rounded-lg;
	}

	/* 渐变按钮的禁用状态特殊处理 */
	.btn-disabled.btn-gradient-primary,
	.btn-disabled.btn-gradient-success,
	.btn-disabled.btn-gradient-warning,
	.btn-disabled.btn-gradient-danger,
	.btn-disabled.btn-gradient-purple {
		@apply shadow-none opacity-50 cursor-not-allowed pointer-events-none text-white !important;
	}

	/* 玻璃态按钮的禁用状态特殊处理 */
	.btn-disabled.btn-glass {
		@apply bg-white/40 backdrop-blur-none;
	}
}
/* 
:root {
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;

  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;

  --vt-c-indigo: #2c3e50;

  --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
  --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
  --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);

  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);
}

:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);

  --section-gap: 160px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);

    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);

    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-2);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */
