import uploadFile from "@/utils/upload_file";

// 定义输入参数的元数据类型
export interface InputMeta {
	type: string;
	name: string;
	default?: any;
	options?: Array<{ value: string; label?: string }>;
	[key: string]: any;
}

// 初始化输入参数根据类型设置默认值
export function initInputValue(inputMeta: InputMeta): any {
	const { type, default: defaultValue, options } = inputMeta;

	if (type === "checkbox") {
		return defaultValue ? defaultValue : [];
	} else if (type === "image") {
		return [];
	} else if (type === "select" || type === "radio") {
		return defaultValue || "";
	} else {
		return defaultValue || "";
	}
}

// 解析工作流数据中的输入节点，返回输入参数的元信息和初始值
export function parseWorkflowInput(data: any): {
	inputMeta: Record<string, InputMeta>;
	inputValues: Record<string, any>;
} {
	const inputMeta: Record<string, InputMeta> = {};
	const inputValues: Record<string, any> = {};

	try {
		const nodes = data.nodes || [];
		const inputNode = nodes.find((node: any) => node.type === "in");

		if (inputNode && inputNode.data && inputNode.data.output) {
			const inputs = inputNode.data.output || [];

			inputs.forEach((input: InputMeta) => {
				inputMeta[input.name] = input;
				inputValues[input.name] = initInputValue(input);
			});
		}
	} catch (error) {
		console.error("解析工作流输入节点失败:", error);
	}

	return { inputMeta, inputValues };
}

// 为API示例生成示例输入值
export function generateExampleInputs(
	inputMeta: Record<string, InputMeta>
): Record<string, any> {
	const exampleInputs: Record<string, any> = {};

	Object.values(inputMeta).forEach((input) => {
		if (input.type === "text" || input.type === "longtext") {
			exampleInputs[input.name] = input.default || "";
		} else if (input.type === "select" || input.type === "radio") {
			exampleInputs[input.name] =
				input.options && input.options.length > 0 ? input.options[0].value : "";
		} else if (input.type === "checkbox") {
			exampleInputs[input.name] =
				input.options && input.options.length > 0 ? [input.options[0].value] : [];
		} else if (input.type === "image") {
			exampleInputs[input.name] = ["https://example.com/image.jpg"];
		} else {
			exampleInputs[input.name] = input.default || "";
		}
	});

	return exampleInputs;
}

// 验证输入值
export function validateInputs(
	inputValues: Record<string, any>,
	inputMeta: Record<string, InputMeta>,
	t: (key: string, options?: any) => string // i18n 函数
): {
	isValid: boolean;
	invalidInputs: string[];
	errorMessages: Record<string, string>;
} {
	const invalidInputs: string[] = [];
	const errorMessages: Record<string, string> = {};

	Object.entries(inputValues).forEach(([key, value]) => {
		const meta = inputMeta[key];
		if (!meta) return;

		if (
			(meta.type === "select" || meta.type === "radio") &&
			(value === "" || value === undefined)
		) {
			invalidInputs.push(key);
			errorMessages[key] = t("pls-select-valid-option");
		} else if (meta.type === "checkbox" && (!Array.isArray(value) || value.length === 0)) {
			invalidInputs.push(key);
			errorMessages[key] = t("pls-select-at-least-one-option");
		} else if (meta.type === "image") {
			const imageArray = Array.isArray(value) ? value : value ? [value] : [];
			if (imageArray.length === 0) {
				invalidInputs.push(key);
				errorMessages[key] = t("pls-upload-image");
			} else if (meta.default && imageArray.length > meta.default) {
				invalidInputs.push(key);
				errorMessages[key] = t("max-upload-image", { count: meta.default });
			}
		}
	});

	return {
		isValid: invalidInputs.length === 0,
		invalidInputs,
		errorMessages,
	};
}

// 处理输入中的图片上传
export async function processImages(
	inputValues: Record<string, any>,
	inputMeta: Record<string, InputMeta>,
	t: (key: string, options?: any) => string, // i18n 函数
	onProgress?: (uploaded: number, total: number) => void
): Promise<Record<string, any>> {
	const processedInput = { ...inputValues };
	let totalImages = 0;
	let uploadedImages = 0;

	// 计算需要上传的总图片数
	for (const [key, value] of Object.entries(processedInput)) {
		const meta = inputMeta[key];
		if (meta && meta.type === "image") {
			if (Array.isArray(value)) {
				totalImages += value.filter((url) => url.startsWith("blob:")).length;
			} else if (value && typeof value === "string" && value.startsWith("blob:")) {
				totalImages += 1;
			}
		}
	}

	// 处理图片上传
	for (const [key, value] of Object.entries(processedInput)) {
		const meta = inputMeta[key];
		if (meta && meta.type === "image") {
			if (Array.isArray(value)) {
				const uploadedUrls = await Promise.all(
					value.map(async (url) => {
						if (url.startsWith("blob:")) {
							const httpUrl = await uploadFile(url, t);
							URL.revokeObjectURL(url); // 清理 blob URL
							uploadedImages++;

							if (onProgress) {
								onProgress(uploadedImages, totalImages);
							}

							return httpUrl;
						}
						return url;
					})
				);
				processedInput[key] = uploadedUrls;
			} else if (value && typeof value === "string" && value.startsWith("blob:")) {
				const httpUrl = await uploadFile(value, t);
				URL.revokeObjectURL(value); // 清理 blob URL
				processedInput[key] = httpUrl;
				uploadedImages++;

				if (onProgress) {
					onProgress(uploadedImages, totalImages);
				}
			}
		}
	}

	return processedInput;
}

// 为API请求准备输入数据（格式化）
export function prepareInputForAPI(
	processedInput: Record<string, any>,
	inputMeta: Record<string, InputMeta>
): Record<string, any> {
	const inputToSend = { ...processedInput };

	for (const [key, value] of Object.entries(inputToSend)) {
		const meta = inputMeta[key];
		if (!meta) continue;

		if (meta.type === "checkbox" && Array.isArray(value)) {
			inputToSend[key] = JSON.stringify(value);
		} else if (meta.type === "image") {
			const imageArray = Array.isArray(value) ? value : value ? [value] : [];
			inputToSend[key] = JSON.stringify(imageArray);
		}
	}

	return inputToSend;
}
