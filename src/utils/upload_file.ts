import { uploadImage } from "@/api/upload";
import type { ComposerTranslation } from "vue-i18n";

async function compressImage(blob: Blob, t: ComposerTranslation): Promise<Blob> {
	return new Promise((resolve, reject) => {
		const img = new Image();
		img.src = URL.createObjectURL(blob);

		img.onload = () => {
			// 计算新的尺寸
			let width = img.width;
			let height = img.height;
			const maxSize = 1024;

			if (width > height && width > maxSize) {
				height = Math.round((height * maxSize) / width);
				width = maxSize;
			} else if (height > maxSize) {
				width = Math.round((width * maxSize) / height);
				height = maxSize;
			}

			// 创建 canvas 进行压缩
			const canvas = document.createElement("canvas");
			canvas.width = width;
			canvas.height = height;
			const ctx = canvas.getContext("2d");

			if (!ctx) {
				reject(new Error(t("failed_to_create_canvas_context")));
				return;
			}

			ctx.drawImage(img, 0, 0, width, height);

			// 转换回 blob
			canvas.toBlob(
				(result) => {
					if (result) {
						resolve(result);
					} else {
						reject(new Error(t("failed_to_compress_image")));
					}
				},
				blob.type,
				0.9 // 质量参数
			);
		};

		img.onerror = () => {
			reject(new Error(t("failed_to_load_image")));
		};
	});
}

export default async function uploadFile(
	blobUrl: string,
	t: ComposerTranslation
): Promise<string> {
	try {
		// 从 blob URL 获取响应
		const response = await fetch(blobUrl);
		const blob = await response.blob();

		// 压缩图片
		const compressedBlob = await compressImage(blob, t);

		// 创建 File 对象
		const file = new File([compressedBlob], "upload.png", { type: blob.type });

		// 上传文件并返回 URL
		return await uploadImage(file);
	} catch (error) {
		console.error(t("failed_to_upload_file"), error);
		throw error;
	}
}
