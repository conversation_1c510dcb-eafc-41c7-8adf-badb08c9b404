import { getManualLLMs, official_llms, type ManualLLM } from "@/api/manual_llm";

// const predefinedModels = [
// 	{
// 		name: "gpt-4o",
// 		value: "gpt-4o",
// 		content_limit: 1000 * 128,
// 		support_function_call: true,
// 		support_json_output: true,
// 		support_vision: true,
// 	},
// 	{
// 		name: "gpt-4o-mini",
// 		value: "gpt-4o-mini",
// 		content_limit: 1000 * 128,
// 		support_function_call: true,
// 		support_json_output: true,
// 		support_vision: true,
// 	},
// 	{
// 		name: "gpt-3.5",
// 		value: "gpt-3.5-turbo",
// 		content_limit: 8000,
// 		support_function_call: true,
// 		support_json_output: false,
// 	},
// 	{
// 		name: "deepseek-chat",
// 		value: "deepseek-chat",
// 		content_limit: 8192,
// 		support_function_call: true,
// 		support_vision: false,
// 	},
// ];

export async function getLLMModels() {
	const manualLLMs = await getManualLLMs();
	const officialLLMs = await official_llms();
	const llms = [...officialLLMs.official_llms, ...manualLLMs.manual_llms];
	const customModels = llms.map((llm: ManualLLM) => ({
		value: llm.model_name,
		name: llm.model_name,
		support_function_call: llm.support_function_call,
		support_json_output: llm.support_json_output,
		support_vision: llm.support_vision,
	}));

	// 合并相同名称的模型
	const mergedModels = customModels.reduce((acc: any, model: any) => {
		const existingModel = acc.find((m: any) => m.value === model.name);
		if (existingModel) {
			// 如果存在相同名称的模型，保留自定义模型的信息
			// Object.assign(existingModel, model);
		} else {
			acc.push(model);
		}
		return acc;
	}, []);

	return mergedModels;
}
