import type { Node, Edge, GraphNode } from "@vue-flow/core";
import { GetNode } from "@/components/nodes/nodes.ts";

export function findParentNode(nodeId: string, allNodes: Node[]): Node | null {
	const node = allNodes.find((n) => n.id === nodeId);
	if (!node) return null;

	if (node.parentNode) {
		const parentNode = allNodes.find((n) => n.id === node.parentNode);
		return parentNode || null;
	}

	return node;
}

export function copySelectedNodes(selectedNodes: Node[], allNodes: Node[]): any[] {
	if (selectedNodes.length === 0) return [];

	const nodesToCopy: any[] = [];
	for (const node of selectedNodes) {
		const parentNode = findParentNode(node.id, allNodes);
		if (!parentNode) continue;

		if (
			node.id === parentNode.id ||
			!nodesToCopy.some((item) => item.parent.id === parentNode.id)
		) {
			const nodeCopy = JSON.parse(JSON.stringify(parentNode));

			const childNodes = allNodes.filter((n) => n.parentNode === parentNode.id);
			const childNodesCopy = JSON.parse(JSON.stringify(childNodes));

			nodesToCopy.push({ parent: nodeCopy, children: childNodesCopy });
		}
	}
	return nodesToCopy;
}

export function InjectVar(data: any, fromNode: Node, curNodeID: string) {
	const firstOutputVar: string | null = getFirstOutput(fromNode) || null;

	if (data.config) {
		for (let i of data.config) {
			if (firstOutputVar && i.injectVar) {
				i.default = "$" + firstOutputVar;
			}
		}
	}

	return { data };
}

export function updateDataByInjectVar(
	refNode: Node | null | undefined,
	targetNode: Node | null | undefined,
	t: any
) {
	if (!targetNode) return;
	if (!refNode) return;

	const firstOutputVar = getFirstOutput(refNode);
	if (!firstOutputVar) return targetNode;

	if (!targetNode.type) return targetNode;
	const targetNodeInitData = GetNode(targetNode.type, t);
	if (!targetNodeInitData) return targetNode;

	if (!targetNodeInitData.config) return targetNode;

	let needInjectVar: string[] = [];
	for (let i of targetNodeInitData.config) {
		// 找到需要注入的变量
		if (i.injectVar) {
			needInjectVar.push(i.name);
		}
	}

	for (let i of needInjectVar) {
		// 仅当当前值不是以$开头的变量表达式时才覆盖
		const currentValue = targetNode.data.input[i]?.trim();
		if (
			/^\$[a-zA-Z_\u4e00-\u9fa5][\w.\u4e00-\u9fa5]*$/.test(currentValue) ||
			currentValue == "" ||
			currentValue == "{}" ||
			(Array.isArray(currentValue) && currentValue.length === 0)
		) {
			targetNode.data.input[i] = "$" + firstOutputVar;
		}
	}

	return targetNode;
}

export function initializeNodeData(
	data: any,
	label: string,
	index: number
	// injectVar: string = ""
) {
	const input = {} as any;
	if (data.config) {
		for (let i of data.config) {
			if (typeof i.default == "object") {
				input[i.name] = JSON.parse(JSON.stringify(i.default || "{}"));
			} else {
				input[i.name] = i.default || "";
			}
		}
	}
	const output = data.output || [];

	return {
		name: index === 0 ? label : label + index,
		output: output,
		input: input,
	};
}

export function getFirstOutput(node: Node): string | null {
	if (!node.data.output || node.data.name == "" || node.data.output.length == 0) return null;
	// return { NodeID: node.id, VarName: node.data.name + "." + node.data.output[0].name };
	return node.data.name + "." + node.data.output[0].name;
}

export function createNewNode(
	data: any,
	fromNode: GraphNode,
	currentNodeID: string,
	typeMap: Map<string, number>
): {
	newNode: Node;
	newEdge: Edge;
} {
	const id = Date.now().toString();
	const type = data.type;
	const label = data.label;

	const leftPadding = Number(fromNode.width || fromNode.dimensions.width) || 0;
	const x = fromNode.position.x + leftPadding + 150;
	const y = fromNode.position.y;

	const count = (typeMap.get(type) || 0) + 1;
	typeMap.set(type, count);

	// let { data: nodeData } = InjectVar(data, fromNode, id);
	let nodeData = initializeNodeData(data, label, count);

	const newNode = {
		id,
		label,
		position: { x, y },
		type,
		data: nodeData,
	};

	const newEdge = {
		id: id + "eg",
		source: currentNodeID,
		type: "custom",
		target: id,
		animated: false,
	};

	return { newNode, newEdge };
}

// 给定一个节点，返回需要更新的所有右侧节点的位置
export function updateRightNodesPosition(
	id: string,
	getConnectedEdges: (nodeId: string) => Edge[],
	findNode: (nodeId: string) => GraphNode | undefined,
	allNodes: Node[]
): {
	nodesToUpdate: string[];
	updatedNodes: { id: string; position: { x: number; y: number } }[];
} {
	const newNode = findNode(id);
	if (!newNode) return { nodesToUpdate: [], updatedNodes: [] };

	const updatedNodes: { id: string; position: { x: number; y: number } }[] = [];

	if (newNode.type === "llm_cls" || newNode.type === "condition") {
		// 对于 llm_cls 和 condition，更新所有右侧节点
		allNodes.forEach((node) => {
			if (node.position.x > newNode.position.x && node.id !== id) {
				updatedNodes.push({
					id: node.id,
					position: { ...node.position, x: node.position.x + 280 },
				});
			}
		});
	} else {
		// 对于其他类型的节点，只更新直接连接的下游节点
		const updateConnectedNodes = (nodeId: string) => {
			const edges = getConnectedEdges(nodeId);
			edges.forEach((edge) => {
				if (edge.source === nodeId) {
					const targetNode = findNode(edge.target);
					if (targetNode && !updatedNodes.some((n) => n.id === targetNode.id)) {
						updatedNodes.push({
							id: targetNode.id,
							position: {
								...targetNode.position,
								x: targetNode.position.x + 300,
							},
						});
						updateConnectedNodes(targetNode.id); // 递归更新下游节点
					}
				}
			});
		};

		updateConnectedNodes(id);
	}

	// 对需要更新的节点进行排序,确保从左到右更新
	updatedNodes.sort((a, b) => a.position.x - b.position.x);

	const nodesToUpdate = updatedNodes.map((node) => node.id);

	return { nodesToUpdate, updatedNodes };
}

export function generateNewNodeId(type: string): string {
	return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function createNewNodeFromCopy(
	parent: Node,
	children: Node[],
	typeMap: Map<string, number>,
	t?: any,
	position?: { x: number; y: number }
): { newParentNode: Node; newChildNodes: Node[] } {
	const nodeType = parent.type;
	if (!nodeType) return { newParentNode: parent, newChildNodes: [] };
	const count = (typeMap.get(nodeType) || 0) + 1;
	typeMap.set(nodeType, count);

	const newParentId = generateNewNodeId(nodeType);
	const newName = `${GetNode(nodeType, t).label}${count}`;

	const newParentNode = {
		...parent,
		id: newParentId,
		label: newName,
		position: position || {
			x: parent.position.x + 300,
			y: parent.position.y + 100,
		},
		selected: false,
		data: {
			...parent.data,
			name: newName,
			input: { ...parent.data.input },
		},
	};

	const newChildNodes: Node[] = [];
	const inputKey = parent.type === "condition" ? "options" : "class";
	if (newParentNode.data.input[inputKey]) {
		newParentNode.data.input[inputKey] = newParentNode.data.input[inputKey].map(
			(item: any, index: number) => {
				const newChildId = generateNewNodeId(children[index].type || "");
				const newChildNode = {
					...children[index],
					id: newChildId,
					parentNode: newParentId,
					position: { ...children[index].position },
					data: {
						...children[index].data,
						input: {
							...children[index].data.input,
							id: newChildId,
						},
					},
				};
				newChildNodes.push(newChildNode);

				return {
					...item,
					id: newChildId,
				};
			}
		);
	}

	return { newParentNode, newChildNodes };
}

export function handleOptionActions(
	optionActionsData: any,
	newNode: Node,
	addEdges: (edges: Edge[]) => void,
	removeEdges: (edges: Edge[]) => void,
	getConnectedEdges: (nodeId: string) => Edge[],
	findNode: (nodeId: string) => GraphNode | undefined,
	allNodes: Node[],
	fromNode: GraphNode
): {
	nodesToUpdate: string[];
	updatedNodes: { id: string; position: { x: number; y: number } }[];
} {
	let targetNodeID = optionActionsData["flowai_target"];
	let insertIntoMiddle = optionActionsData["flowai_insertIntoMiddle"];

	if (insertIntoMiddle) {
		if (
			targetNodeID != "" &&
			newNode.type != "llm_cls" &&
			newNode.type != "out" &&
			newNode.type != "condition"
		) {
			addEdges([
				{
					id: newNode.id + "eg2",
					source: newNode.id,
					type: "custom",
					target: targetNodeID,
					animated: false,
				},
			]);
		}

		let delete_edge = optionActionsData["flowai_delete_edge"];
		if (delete_edge) {
			removeEdges([delete_edge]);
		}

		return updateRightNodesPosition(newNode.id, getConnectedEdges, findNode, allNodes);
	}

	return { nodesToUpdate: [], updatedNodes: [] };
}
