// 变量引用的依赖收集

export type VarLinkDep = {
	NodeID: string;
	VarName: string;
};
const deps = new Map<VarLinkDep, VarLinkDep[]>();

export function addVarLinkDep(baseNode: VarLinkDep, beingDependOn: VarLinkDep[]) {
	if (deps.has(baseNode)) {
		deps.get(baseNode)?.push(...beingDependOn);
	} else {
		deps.set(baseNode, beingDependOn);
	}
	console.log("addVarLinkDep", deps);
}

export function getVarLinkDep(baseNode: VarLinkDep) {
	return deps.get(baseNode);
}

export function clearVarLinkDep() {
	deps.clear();
}

export function deleteVarLinkDep(baseNode: VarLinkDep, beingDependOn: VarLinkDep) {
	if (deps.has(baseNode)) {
		deps.get(baseNode)?.filter((item) => item.NodeID !== beingDependOn.NodeID);
	}
}
