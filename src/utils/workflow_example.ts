import llm_process from "./example/llm_process.json?raw";
import multi_llm_assistant from "./example/multi_llm_assistant.json?raw";
import logic_flow from "./example/logic_flow.json?raw";
import logic_flow_en from "./example/logic_flow_en.json?raw";
import llm_process_en from "./example/llm_process_en.json?raw";
import multi_llm_assistant_en from "./example/multi_llm_assistant_en.json?raw";

const workflowExample = {
	llmDataProcessing: JSON.parse(llm_process),
	multiLlmAssistant: JSON.parse(multi_llm_assistant),
	logicFlow: JSON.parse(logic_flow),
	logicFlow_En: JSON.parse(logic_flow_en),
	llmDataProcessing_En: JSON.parse(llm_process_en),
	multiLlmAssistant_En: JSON.parse(multi_llm_assistant_en),
};

export default workflowExample;
