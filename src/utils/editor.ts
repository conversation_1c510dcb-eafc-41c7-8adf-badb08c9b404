export const varClass = `bg-primary p-1 rounded-md text-white m-1 inline-block`;
export function htmlToCustomText(html = "") {
	// 创建一个新的DOMParser实例
	const parser = new DOMParser();
	// 使用DOMParser解析HTML字符串
	const doc = parser.parseFromString(html, "text/html");

	// 遍历所有的br元素，将它们替换为\n
	doc.querySelectorAll("br").forEach((br) => {
		br.parentNode?.replaceChild(document.createTextNode("\n"), br);
	});

	// 遍历所有的span元素
	doc.querySelectorAll("span").forEach((span) => {
		// 为span元素的文本内容添加$前缀，并确保后面跟着一个空格
		span.textContent = "$" + span.textContent + " ";
	});

	// 遍历所有的div元素
	doc.querySelectorAll("div").forEach((div) => {
		// 检查div是否只包含一个\n（之前将<br>替换成了\n）
		const onlyContainsBR = div.childNodes.length === 1 && div.textContent === "\n";
		const isEmptyDiv = div.textContent?.trim() === "";

		// 如果div不是其父元素的第一个子节点，并且div不仅仅包含一个\n，那么在它之前添加一个换行符
		if (div.previousSibling && div.previousSibling.textContent !== "\n") {
			div.parentNode?.insertBefore(document.createTextNode("\n"), div);
		}

		// 如果div不仅仅包含一个\n，那么在它内容后面追加一个换行符\n
		if (!onlyContainsBR && !isEmptyDiv) {
			div.appendChild(document.createTextNode("\n"));
		}
	});

	// 返回处理后的文本内容
	// 使用textContent会获取所有文本，包括新添加的换行符

	return doc.body.textContent; //?.replace("&lt;", "<").replace("&gt;", ">");
}

export function customTextToHtml(text = "") {
	// 遍历每一行
	const lines = text.split("\n");
	let html = [];
	for (let line of lines) {
		// 遍历所有的标签，转译使其能在html中显示出来
		const tags = line.match(/<[^>]+>/g);
		if (tags) {
			for (const tag of tags) {
				line = line.replace(tag, tag.replace(/</g, "&lt;").replace(/>/g, "&gt;"));
			}
		}

		line = line.replace(/\t/g, "&nbsp;&nbsp;");
		line = line.replace(/\s/g, "&nbsp;");

		// 遍历每个变量
		const variables = line.match(/\$[\w.\u4e00-\u9fa5]+/g);
		if (variables) {
			for (const variable of variables) {
				// 将变量替换为 <span class="bg-primary p-1 rounded-md text-white m-1 inline-block">变量名</span>
				line = line.replace(
					variable,
					`<span class="${varClass}" contenteditable="false">${variable.slice(
						1
					)}</span>`
				);
			}
		}
		html.push(line);
	}
	return html.join("<br>");
}
