import { useI18n } from "vue-i18n";
import type { ComposerTranslation } from "vue-i18n";
// 定义条件类型枚举
export enum ConditionType {
	EQUALS = "EQUALS",
	NOT_EQUALS = "NOT_EQUALS",
	CONTAINS = "CONTAINS",
	NOT_CONTAINS = "NOT_CONTAINS",
	STARTS_WITH = "STARTS_WITH",
	NOT_STARTS_WITH = "NOT_STARTS_WITH",
	ENDS_WITH = "ENDS_WITH",
	NOT_ENDS_WITH = "NOT_ENDS_WITH",
}

// 条件类型对应的显示标签
export const conditionTypes = (t: ComposerTranslation): Record<ConditionType, string> => {
	return {
		[ConditionType.EQUALS]: t("condition_types.equals"),
		[ConditionType.NOT_EQUALS]: t("condition_types.not_equals"),
		[ConditionType.CONTAINS]: t("condition_types.contains"),
		[ConditionType.NOT_CONTAINS]: t("condition_types.not_contains"),
		[ConditionType.STARTS_WITH]: t("condition_types.starts_with"),
		[ConditionType.NOT_STARTS_WITH]: t("condition_types.not_starts_with"),
		[ConditionType.ENDS_WITH]: t("condition_types.ends_with"),
		[ConditionType.NOT_ENDS_WITH]: t("condition_types.not_ends_with"),
	};
};

// 获取条件类型标签的函数
export function getConditionTypeLabel(type: ConditionType, t: ComposerTranslation): string {
	return conditionTypes(t)[type] || "Unknown condition";
}
