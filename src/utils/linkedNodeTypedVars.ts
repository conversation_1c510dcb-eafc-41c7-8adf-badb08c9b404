import { useNode } from "@vue-flow/core";

function getLinkedOutput(id: string, visited = new Set<string>()): any[] {
	if (visited.has(id)) {
		return [];
	}
	visited.add(id);

	let node = useNode(id);
	let edges = node.connectedEdges.value || [];
	let varsSet = new Set<any>();

	for (let edge of edges) {
		if (edge.target !== id) continue;
		let output = edge.sourceNode.data.output || [];
		// debugger;
		output = output
			.filter((i: any) => i.name !== "")
			.map((i: any) => ({
				...i,
				node: edge.sourceNode.data.name,
			}));

		output.forEach((item: any) => varsSet.add(JSON.stringify(item)));

		let moreVars = getLinkedOutput(edge.source, visited);
		moreVars.forEach((item: any) => varsSet.add(JSON.stringify(item)));
	}

	if (node.parentNode.value) {
		let moreVars = getLinkedOutput(node.parentNode.value.id, visited);
		moreVars.forEach((item: any) => varsSet.add(JSON.stringify(item)));
	}

	return Array.from(varsSet).map((item: string) => JSON.parse(item));
}

export default getLinkedOutput;
