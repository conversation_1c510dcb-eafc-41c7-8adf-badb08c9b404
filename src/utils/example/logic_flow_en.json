{"nodes": [{"id": "myinput", "type": "in", "connectable": false, "initialized": false, "position": {"x": -56.00000000000003, "y": 188.66666666666669}, "data": {"name": "Start", "output": [{"type": "text", "name": "url", "default": "https://flowai.cc/intro/overview", "options": []}]}, "label": "Start (Input)", "deletable": false}, {"id": "1736845176804", "type": "llm_cls", "initialized": false, "position": {"x": 271, "y": 188.66666666666669}, "data": {"name": "LLMIntentClassification", "output": [], "input": {"condition_content": "$Start.url", "model": "gpt-4o-mini", "class": [{"name": "Is a valid URL", "id": "llm_classifier_class_2k0lbbuh"}, {"name": "Not a valid URL", "id": "llm_classifier_class_rizwk1lj"}], "prompt": ""}}, "label": "LLM Intent Classification"}, {"id": "llm_classifier_class_2k0lbbuh", "type": "llm_classifier_class", "draggable": false, "selectable": false, "connectable": false, "focusable": false, "initialized": false, "position": {"x": 10, "y": 60}, "data": {"name": "Category 1", "input": {"class": "Is a valid URL"}}, "label": "Is a valid URL", "extent": "parent", "parentNode": "1736845176804", "targetPosition": "right"}, {"id": "llm_classifier_class_rizwk1lj", "type": "llm_classifier_class", "draggable": false, "selectable": false, "connectable": false, "focusable": false, "initialized": false, "position": {"x": 10, "y": 110}, "data": {"name": "Category 2", "input": {"class": "Not a valid URL"}}, "label": "Not a valid URL", "extent": "parent", "parentNode": "1736845176804", "targetPosition": "right"}, {"id": "1736845220934", "type": "template", "connectable": false, "initialized": false, "position": {"x": 713.9496281362613, "y": 542.5194258342001}, "data": {"name": "ErrorContentPrompt", "output": [{"name": "template", "type": "string", "desc": "Concatenated data"}], "input": {"template": "Please enter a valid URL!"}}, "label": "Content Concatenator"}, {"id": "1736845233904", "type": "out", "initialized": false, "position": {"x": 764.922435437745, "y": 744.4923372165927}, "data": {"name": "ErrorOutput", "output": [], "input": {"from": "$ErrorContentPrompt.template"}}, "label": "End (Output)"}, {"id": "1736845244617", "type": "browser", "connectable": false, "initialized": false, "position": {"x": 715.8604739913953, "y": 180.66666666666666}, "data": {"name": "WebContentScraper", "output": [{"name": "body", "type": "string", "desc": "Web page content"}], "input": {"url": "$Start.url"}}, "label": "Web Content Scraper"}, {"id": "1736845303532", "type": "llm", "connectable": false, "initialized": false, "position": {"x": 1052.63947396815, "y": 326.0116589698837}, "data": {"name": "LLM", "output": [{"name": "result", "type": "string", "desc": "Output result of the large language model"}], "input": {"model": "gpt-4o-mini", "system": "", "prompt": "Please summarize the content as concisely as possible:\n```\n$WebContentScraper.body\n```", "max_tokens": 0}}, "label": "LLM"}, {"id": "1736845387398", "type": "template", "connectable": false, "initialized": false, "position": {"x": 1355.9999306127272, "y": 341.11243739099723}, "data": {"name": "OutputContentConcatenation", "output": [{"name": "template", "type": "string", "desc": "Concatenated data"}], "input": {"template": "## Summary\n$LLM.result\n\n## Original Text\n$WebContentScraper.body"}}, "label": "Content Concatenator"}, {"id": "1736845457316", "type": "out", "initialized": false, "position": {"x": 1635.3603872573046, "y": 337.3372427857188}, "data": {"name": "EndOutput", "output": [], "input": {"from": "$OutputContentConcatenation.template"}}, "label": "End (Output)"}], "edges": [{"id": "1736845176804eg", "type": "custom", "source": "myinput", "target": "1736845176804", "data": {}, "label": "", "animated": false, "sourceX": 145.9999799147844, "sourceY": 219.66668025589294, "targetX": 269.00001548788373, "targetY": 269.666696293097}, {"id": "1736845220934eg", "type": "custom", "source": "llm_classifier_class_rizwk1lj", "target": "1736845220934", "data": {}, "label": "", "animated": false, "sourceX": 562.999994053331, "sourceY": 317.66665797339607, "targetX": 711.9495860192453, "targetY": 563.5193440481461}, {"id": "1736845233904eg", "type": "custom", "source": "1736845220934", "target": "1736845233904", "data": {}, "label": "", "animated": false, "sourceX": 915.9497232608451, "sourceY": 563.5193440481461, "targetX": 762.9225085305284, "targetY": 775.4922355960196}, {"id": "1736845244617eg", "type": "custom", "source": "llm_classifier_class_2k0lbbuh", "target": "1736845244617", "data": {}, "label": "", "animated": false, "sourceX": 562.999994053331, "sourceY": 267.66671557829574, "targetX": 713.8604318743794, "targetY": 211.66662265099322}, {"id": "1736845387398eg", "type": "custom", "source": "1736845303532", "target": "1736845387398", "data": {}, "label": "", "animated": false, "sourceX": 1254.6394538829345, "sourceY": 357.01161495421024, "targetX": 1354.00011891531, "targetY": 362.1124132098429}, {"id": "1736845457316eg", "type": "custom", "source": "1736845387398", "target": "1736845457316", "data": {}, "label": "", "animated": false, "sourceX": 1558.0001409471104, "sourceY": 362.1124132098429, "targetX": 1633.3603451402887, "targetY": 368.33719877004535}, {"id": "vueflow__edge-1736845244617-1736845303532", "type": "custom", "source": "1736845244617", "target": "1736845303532", "sourceHandle": null, "targetHandle": null, "data": {}, "label": "", "sourceX": 917.8604539061798, "sourceY": 211.66662265099322, "targetX": 1050.639431851134, "targetY": 357.01161495421024}], "position": [89.59554346898324, 81.29537461713471], "zoom": 0.5950502892188553, "viewport": {"x": 89.59554346898324, "y": 81.29537461713471, "zoom": 0.5950502892188553}}