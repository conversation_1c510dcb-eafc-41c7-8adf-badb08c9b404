{"nodes": [{"id": "myinput", "type": "in", "connectable": false, "initialized": false, "position": {"x": 92.80937477225308, "y": 56.187495445061586}, "data": {"name": "开始", "output": [{"type": "longtext", "name": "输入", "default": "8:00 从酒店出发，前往西湖景区。\n8:30 到达西湖，开始环湖步行，欣赏断桥残雪、苏堤春晓等景点。\n10:00 乘船游湖，前往三潭印月，感受湖光山色。\n12:00 在楼外楼享用午餐，品尝西湖醋鱼、龙井虾仁等特色菜肴。\n14:00 参观雷峰塔，了解白娘子传说，登塔俯瞰西湖全景。\n16:00 前往灵隐寺，参观飞来峰石刻，感受佛教文化。\n18:00 在河坊街品尝当地小吃，如葱包桧、定胜糕等。\n20:00 返回酒店，结束一天的行程。", "options": []}, {"type": "longtext", "name": "指令", "default": "这是用户的游玩记录，请你按照“上午、下午、晚上”为类别，生成JSON数据，比如：\n{\n   \"上午\": [\"xxx\",\"xxx\"]\n}", "options": [{"value": "选项1"}]}]}, "label": "开始（输入）", "deletable": false}, {"id": "1736842459049", "type": "llm", "connectable": false, "initialized": false, "position": {"x": 410.0390615510545, "y": 207.19062522774692}, "data": {"name": "LLM", "output": [{"name": "result", "type": "string", "desc": "大语言模型的输出结果"}], "input": {"model": "gpt-4o-mini", "system": "你是一个结构化数据处理专家，你会负责将数据转化为JSON结构并输出！", "prompt": "这是数据：\n```\n$开始.输入\n```\n$开始.指令\n务必以JSON输出", "max_tokens": 0, "json_response": true}}, "label": "LLM"}, {"id": "1736842559736", "type": "out", "initialized": false, "position": {"x": 966.5062474188683, "y": 463.65625835072046}, "data": {"name": "结束（输出）", "output": [], "input": {"from": "$内容拼接器.template"}}, "label": "结束（输出）"}, {"id": "1736842868697", "type": "template", "initialized": false, "position": {"x": 685.6734357159825, "y": 374.7099012613017}, "data": {"name": "内容拼接器", "output": [{"name": "template", "type": "string", "desc": "拼接后的数据"}], "input": {"template": "今天的游玩日志，结构化数据：\n```json\n$LLM.result\n```"}}, "label": "内容拼接器"}], "edges": [{"id": "1736842459049eg", "type": "custom", "source": "myinput", "target": "1736842459049", "data": {}, "label": "", "animated": false, "sourceX": 294.8093747722531, "sourceY": 95.18749544506159, "targetX": 408.0390615510545, "targetY": 238.19062522774692}, {"id": "1736842868697eg", "type": "custom", "source": "1736842459049", "target": "1736842868697", "data": {}, "label": "", "animated": false, "sourceX": 619.0390615510545, "sourceY": 238.19062522774692, "targetX": 683.6734357159825, "targetY": 395.7099012613017}, {"id": "1736842868697eg2", "type": "custom", "source": "1736842868697", "target": "1736842559736", "data": {}, "label": "", "animated": false, "sourceX": 887.6734357159825, "sourceY": 395.7099012613017, "targetX": 964.5062474188683, "targetY": 494.65625835072046}], "position": [98.32727349484026, 106.00913029886807], "zoom": 0.8344197910422891, "viewport": {"x": 98.32727349484026, "y": 106.00913029886807, "zoom": 0.8344197910422891}}