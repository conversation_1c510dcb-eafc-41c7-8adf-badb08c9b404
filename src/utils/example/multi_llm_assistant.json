{"nodes": [{"id": "myinput", "type": "in", "connectable": false, "initialized": false, "position": {"x": 76.59117483809177, "y": 198.33524780918137}, "data": {"name": "开始", "output": [{"type": "longtext", "name": "英文原文", "default": "The labyrinthine convolutions of temporal ephemerality, wherein the gossamer threads of reminiscence intertwine with the nebulous fabric of anticipation, render the present moment an elusive chimera, perpetually suspended betwixt the ossified relics of yesteryear and the inchoate whispers of tomorrow.", "options": []}]}, "label": "开始（输入）", "deletable": false}, {"id": "1736844591745", "type": "llm", "connectable": false, "initialized": false, "position": {"x": 410.5911748380918, "y": 198.33524780918137}, "data": {"name": "LLM直译", "output": [{"name": "result", "type": "string", "desc": "大语言模型的输出结果"}], "input": {"model": "gpt-4o-mini", "system": "你是一个专业的直译助手，你的任务是将输入的文本逐字逐句翻译成[中文]，严格保持原文的句式、结构和用词风格，不做任何本土化或意译处理。你的翻译需要尽可能贴近原文的字面意思，即使翻译结果显得生硬或不自然也没关系。\n", "prompt": "请翻译以下文本：\n\n$开始.英文原文", "max_tokens": 0}}, "label": "LLM"}, {"id": "1736844743690", "type": "llm", "connectable": false, "initialized": false, "position": {"x": 676.5579046681257, "y": 358.60922362912845}, "data": {"name": "LLM本土化翻译", "output": [{"name": "result", "type": "string", "desc": "大语言模型的输出结果"}], "input": {"model": "gpt-4o-mini", "system": "你是一个专业的本土化翻译助手，你的任务是将输入的文本翻译成[中文]，并在直译的基础上进行优化，使翻译结果更符合[中文]的文化习惯和表达方式。你需要确保翻译结果流畅自然，同时保留原文的核心意思。\n", "prompt": "请翻译以下文本：\n\n$LLM直译.result", "max_tokens": 0}}, "label": "LLM"}, {"id": "1736844800772", "type": "out", "initialized": false, "position": {"x": 1132.868767056324, "y": 623.0802359014147}, "data": {"name": "结束（输出）", "output": [], "input": {"from": "$内容拼接器.template"}}, "label": "结束（输出）"}, {"id": "1736844845923", "type": "template", "connectable": false, "initialized": false, "position": {"x": 876.6269298668615, "y": 633.1234388990963}, "data": {"name": "内容拼接器", "output": [{"name": "template", "type": "string", "desc": "拼接后的数据"}], "input": {"template": "## 英文原文\n$开始.英文原文\n\n## 直译\n$LLM直译.result\n\n## 本土化翻译\n$LLM本土化翻译.result"}}, "label": "内容拼接器"}], "edges": [{"id": "1736844591745eg", "type": "custom", "source": "myinput", "target": "1736844591745", "data": {}, "label": "", "animated": false, "sourceX": 285.59036378281655, "sourceY": 229.33514151748827, "targetX": 408.59116000625943, "targetY": 229.33513520091645}, {"id": "1736844743690eg", "type": "custom", "source": "1736844591745", "target": "1736844743690", "data": {}, "label": "", "animated": false, "sourceX": 619.5902473268463, "sourceY": 229.33514151748827, "targetX": 674.5578898362934, "targetY": 389.6091110208635}, {"id": "1736844845923eg", "type": "custom", "source": "1736844743690", "target": "1736844845923", "data": {}, "label": "", "animated": false, "sourceX": 885.5570936128505, "sourceY": 389.60911733743535, "targetX": 874.6269150350291, "targetY": 654.1233685876398}, {"id": "vueflow__edge-1736844845923-1736844800772", "type": "custom", "source": "1736844845923", "target": "1736844800772", "sourceHandle": null, "targetHandle": null, "data": {}, "label": "", "sourceX": 1085.626002355616, "sourceY": 654.1234040182042, "targetX": 1130.8687522244916, "targetY": 654.0800941791572}], "position": [135.0914880292081, 78.61189246494288], "zoom": 0.5974189709582656, "viewport": {"x": 135.0914880292081, "y": 78.61189246494288, "zoom": 0.5974189709582656}}