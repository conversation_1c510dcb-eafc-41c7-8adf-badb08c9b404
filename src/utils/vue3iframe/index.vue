<template>
	<div class="vue3-iframe">
		<div :style="{ maxHeight: containerStyle.maxHeight }" v-show="$slots.default">
			<iframe
				ref="iframeRef"
				style="display: block; width: 100%; height: 100%; border: none"
				:style="iframeStyle"
				:onload="onLoad"
			/>
			<Teleport v-if="hasLoad" :to="iframeRef?.contentWindow?.document.body">
				<slot />
			</Teleport>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { ref, computed, nextTick, type PropType, onUnmounted } from "vue";
	import { useElementSize } from "@vueuse/core";

	const props = defineProps({
		width: {
			type: String,
			default: "auto",
		},
		maxHeight: {
			type: String,
			default: "auto",
		},
		height: {
			type: String,
			default: "auto",
		},
		inheritStyles: Boolean,
		styles: {
			type: Array as PropType<string[]>,
			default: () => [],
		},
	});

	const iframeRef = ref<HTMLIFrameElement>();
	const hasLoad = ref(false);
	const iframeBody = ref();
	const { height: bodyHeight } = useElementSize(iframeBody);

	const emit = defineEmits(["ready"]);

	const observer = ref<MutationObserver>();

	onUnmounted(() => {
		observer.value?.disconnect();
	});

	const onLoad = () => {
		Promise.resolve().then(() => {
			if (props.inheritStyles) {
				inheritStyles();
			}

			observer.value = new MutationObserver((mutations) => {
				mutations.forEach((mutation) => {
					mutation.addedNodes.forEach((node) => {
						if (
							node.nodeName === "STYLE" ||
							(node.nodeName === "LINK" &&
								(node as HTMLLinkElement).rel === "stylesheet")
						) {
							const clonedNode = node.cloneNode(true);
							iframeRef.value?.contentDocument?.head.appendChild(clonedNode);
						}
					});
				});
			});

			observer.value.observe(document.head, {
				childList: true,
				subtree: true,
			});

			props.styles.forEach((el) => {
				if (iframeRef.value) {
					insertStyle(iframeRef.value, el);
				}
			});

			// 增加内容文档就绪检查，并添加键盘事件监听器
			nextTick(() => {
				hasLoad.value = true;
				iframeBody.value = iframeRef.value?.contentWindow?.document.body;

				// 添加键盘事件监听器，将事件转发到父窗口
				iframeRef.value?.contentWindow?.document.addEventListener(
					"keydown",
					(event) => {
						// 创建一个新的自定义事件，包含原始事件的关键信息
						const customEvent = new CustomEvent("iframe-keydown", {
							detail: {
								key: event.key,
								ctrlKey: event.ctrlKey,
								metaKey: event.metaKey,
								altKey: event.altKey,
								shiftKey: event.shiftKey,
								originalEvent: event,
							},
						});
						// 将事件分发到父窗口
						window.dispatchEvent(customEvent);
					}
				);

				emit("ready");
			});
		});
	};

	const iframeStyle = computed(() => {
		const height = props.height === "auto" ? `${bodyHeight.value}px` : props.height;
		const width = props.width === "auto" ? "100%" : props.width;
		return {
			height,
			width,
		};
	});

	const containerStyle = computed(() => {
		return {
			maxHeight: props.maxHeight,
		};
	});

	const inheritStyles = () => {
		nextTick(() => {
			const styles = Array.from(
				document.querySelectorAll('style, link[rel="stylesheet"]')
			);
			styles.forEach((el: any) => {
				const clonedNode = el.cloneNode(true);
				// 处理link标签的重新加载
				if (clonedNode.tagName === "LINK" && clonedNode.rel === "stylesheet") {
					const newLink = document.createElement("link");
					newLink.rel = "stylesheet";
					newLink.href = clonedNode.href;
					iframeRef.value?.contentDocument?.head.appendChild(newLink);
				} else {
					iframeRef.value?.contentDocument?.head.appendChild(clonedNode);
				}
			});
		});
	};

	const insertStyle = (target: HTMLIFrameElement, style: string) => {
		return new Promise((resolve) => {
			const styleEl = document.createElement("style");
			styleEl.innerHTML = style;
			// type="text/css" is deprecated and unnecessary in HTML5

			styleEl.onload = () => resolve(styleEl);

			target?.contentWindow?.document
				.getElementsByTagName("head")
				.item(0)
				?.appendChild(styleEl);
		});
	};
</script>
