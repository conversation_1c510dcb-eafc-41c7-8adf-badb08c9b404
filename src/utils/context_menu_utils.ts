import type { Node } from "@vue-flow/core";

// 获取相对于容器的坐标
export const getLocalPosition = (event: MouseEvent, container: HTMLElement) => {
	const rootRect = container.getBoundingClientRect();
	return {
		x: event.clientX - rootRect.left,
		y: event.clientY - rootRect.top,
	};
};

// 处理节点右键菜单
export const handleNodeContextMenu = (
	nodeId: string,
	position: { x: number; y: number },
	findNode: (id: string) => Node | undefined,
	setNodes: (updater: (nodes: Node[]) => Node[]) => void
) => {
	const node = findNode(nodeId);
	if (!node) return null;
	if (node.parentNode) {
		return null;
	}

	// 选中节点
	setNodes((nds) =>
		nds.map((n) => ({
			...n,
			selected: n.id === nodeId,
		}))
	);

	// 返回菜单配置
	return {
		show: true,
		x: position.x,
		y: position.y,
		type: "node" as const,
	};
};

// 处理画布右键菜单
export const handlePaneContextMenu = (position: { x: number; y: number }) => {
	return {
		show: true,
		x: position.x,
		y: position.y,
		type: "pane" as const,
	};
};
