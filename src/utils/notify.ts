import { notify } from "notiwind";

export const Success = (title: string, message: string) => {
	notify(
		{
			type: "success",
			group: "notify",
			title: title,
			text: message,
		},
		4000
	); // 4s
};

export const Error = (title: string = "", message: string = "") => {
	notify(
		{
			type: "error",
			group: "notify",
			title: title,
			text: message,
		},
		4000
	);
};

export const Info = (title: string, message: string) => {
	notify(
		{
			type: "info",
			group: "notify",
			title: title,
			text: message,
		},
		4000
	);
};

export const Warning = (title: string, message: string) => {
	notify(
		{
			type: "warning",
			group: "notify",
			title: title,
			text: message,
		},
		4000
	);
};
