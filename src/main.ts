import "./assets/main.css";
import { MotionPlugin } from "@vueuse/motion";
import Notifications from "notiwind";
import { createApp } from "vue";
import { createPinia } from "pinia";
import Clarity from "@microsoft/clarity";
import App from "./App.vue";
import router from "./router";
import { createI18n } from "vue-i18n";
import en from "./locales/en.json";
import zh from "./locales/zh.json";

const messages = { en, zh };

const userLanguage = navigator.language;
const storedLocale = localStorage.getItem("locale");
const locale = storedLocale || (userLanguage.startsWith("zh") ? "zh" : "en");

const i18n = createI18n({
	locale: locale,
	fallbackLocale: "en",
	messages,
});

const app = createApp(App);

app.use(MotionPlugin);
app.use(Notifications);
app.use(createPinia());
app.use(router);
app.use(i18n);
const projectId = "plt8z3yibx";
Clarity.init(projectId);
app.mount("#app");
