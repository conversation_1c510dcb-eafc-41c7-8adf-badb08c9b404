<template>
	<div class="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12">
		<div class="absolute top-4 right-4">
			<select
				@change="(e) => changeLanguage((e.target as HTMLSelectElement).value)"
				class="select select-ghost select-xs"
			>
				<option value="en" :selected="locale === 'en'">EN</option>
				<option value="zh" :selected="locale === 'zh'">中</option>
			</select>
		</div>
		<div class="relative py-3 sm:max-w-xl sm:mx-auto w-full px-4 sm:px-0">
			<div
				class="absolute inset-0 bg-gradient-to-r from-primary to-secondary shadow-lg transform -skew-y-6 sm:skew-y-0 sm:-rotate-6 sm:rounded-3xl"
			></div>
			<div class="relative px-4 py-10 bg-white shadow-lg rounded-3xl sm:p-20">
				<div class="max-w-md mx-auto">
					<div class="flex items-center gap-2 justify-center sm:justify-start">
						<router-link to="/" class="flex items-center gap-2">
							<img src="/main_logo.png" class="w-40 max-sm:w-32" />
						</router-link>
					</div>
					<div class="mt-8" @keydown.enter.prevent="login">
						<div
							class="py-8 text-base leading-6 space-y-4 text-gray-700 sm:text-lg sm:leading-7"
						>
							<div class="flex flex-col">
								<label for="loginIdentifier" class="font-semibold">{{
									t("usernameOrEmail")
								}}</label>
								<input
									id="loginIdentifier"
									v-model="loginIdentifier"
									class="input input-bordered w-full mt-2"
								/>
							</div>
							<div class="flex flex-col pt-4">
								<label for="password" class="font-semibold">{{
									t("login_password")
								}}</label>
								<input
									id="password"
									type="password"
									v-model="password"
									class="input input-bordered w-full mt-2"
								/>
							</div>
						</div>
						<div class="pt-4 text-base leading-6 font-bold sm:text-lg text-center">
							<span
								v-if="loading"
								class="loading loading-ring loading-lg"
							></span>
							<button
								v-if="!loading"
								class="btn btn-primary w-full"
								@click="login"
							>
								{{ t("login") }}
							</button>
							<div v-if="!loading" class="divider">{{ t("or") }}</div>
							<button
								v-if="!loading"
								class="btn btn-outline w-full flex items-center justify-center gap-2"
								@click="googleLogin"
							>
								<img
									src="@/assets/google.svg"
									alt="Google Logo"
									width="18"
									height="18"
								/>
								{{ t("login_with_google") }}
							</button>
							<button
								v-if="!loading"
								class="btn btn-outline w-full flex items-center justify-center gap-2 mt-4"
								@click="githubLogin"
							>
								<img
									src="@/assets/github.svg"
									alt="Github Logo"
									width="18"
									height="18"
								/>
								{{ t("login_with_github") }}
							</button>
						</div>
						<div class="pt-4 text-center">
							<p class="text-sm text-gray-600">
								{{ t("signup_msg") }}
								<router-link
									to="/register"
									class="text-primary hover:underline"
								>
									{{ t("signup") }}
								</router-link>
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import {
		me,
		login as loginAPI,
		googleLogin as googleLoginAPI,
		githubLogin as githubLoginAPI,
	} from "@/api/user";
	import { useUserStore } from "@/stores/user";
	import { Error } from "@/utils/notify";
	import { ref } from "vue";
	import { useRouter } from "vue-router";
	import { useI18n } from "vue-i18n";

	const { t, locale } = useI18n();
	const loading = ref(false);
	const loginIdentifier = ref("");
	const password = ref("");
	const userStore = useUserStore();
	const router = useRouter();

	function login() {
		if (!loginIdentifier.value || !password.value) {
			Error(t("error"), t("enter_username_or_email_and_password"));
			return;
		}
		loading.value = true;
		loginAPI(loginIdentifier.value, password.value)
			.then((res) => {
				userStore.setUser(res.data);
				router.push({ name: "dashboard" });
			})
			.catch((err) => {
				Error(t("login_error"), err);
			})
			.finally(() => {
				loading.value = false;
			});
	}

	function googleLogin() {
		loading.value = true;
		googleLoginAPI()
			.then((res: any) => {
				window.location.href = res.url;
			})
			.catch((err) => {
				Error(t("google-login-error"), err);
			})
			.finally(() => {
				loading.value = false;
			});
	}

	function githubLogin() {
		loading.value = true;
		githubLoginAPI()
			.then((res: any) => {
				window.location.href = res.url;
			})
			.catch((err) => {
				Error(t("github-login-error"), err);
			})
			.finally(() => {
				loading.value = false;
			});
	}

	const changeLanguage = (lang: string) => {
		locale.value = lang;
		localStorage.setItem("locale", lang);
	};
</script>
