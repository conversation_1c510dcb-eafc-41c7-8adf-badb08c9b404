<script setup lang="ts">
	import type { FlowExportObject } from "@vue-flow/core";
	import { ref, watch, defineAsyncComponent, Teleport } from "vue";
	import { onBeforeRouteLeave, onBeforeRouteUpdate, useRoute, useRouter } from "vue-router";
	import { useCurNav } from "@/stores/curNav";
	import { Error, Success } from "@/utils/notify";
	import type { Project } from "@/api/projects";
	import {
		createProject,
		updateProject,
		getProject,
		publishedVersions,
		type PublishedVersion,
	} from "@/api/projects";
	import component_wait from "@/components/utils/component_wait.vue";
	import {
		ArrowLeftIcon,
		PlayIcon,
		BookmarkIcon,
		CodeBracketIcon,
	} from "@heroicons/vue/24/outline";
	import { useNodeOptions } from "@/composables/useNodeOptions";
	import workflowExample from "@/utils/workflow_example";
	import { useI18n } from "vue-i18n";
	import PublishModal from "@/components/workflow/PublishModal.vue";
	const { t, locale } = useI18n();

	const Workflow = defineAsyncComponent(() => import("@/components/workflow.vue"));
	const Runtime = defineAsyncComponent(() => import("@/components/runtime.vue"));
	const Config = defineAsyncComponent(() => import("@/components/config.vue"));
	// 生成唯一的 flow ID
	const flowId = ref(`vue-flow-${Date.now()}`);
	const curNavStore = useCurNav();
	curNavStore.setCurNav("");

	const router = useRouter();
	const currentProjectID = ref("");
	const saveModel = ref(false);
	const isInit = ref(false);
	const needSave = ref(false);
	const currentSavedData = ref({});
	const projectName = ref("");
	const projectDesc = ref("");
	const saveLoading = ref(false);
	const loadLoading = ref(true);
	const isPublished = ref(false);
	const showPublishModal = ref(false);
	const publishLoading = ref(false);
	const loadingPublishedVersion = ref(false);
	const publishedVersionList = ref<PublishedVersion[]>([]);
	const deleteVersionLoading = ref(false);
	const deleteVersionId = ref("");
	const onWorkflowSave = () => {
		const { flowObject, hasOutputNode } = workflowRef.value?.save() || {};
		if (!flowObject) return;

		if (!hasOutputNode) {
			const confirmSave = confirm(t("current-workflow-has-no-output-node-confirm"));
			if (!confirmSave) return;
		}

		saveModel.value = true;
		currentSavedData.value = flowObject;
	};
	const workflowSaveProcess = () => {
		if (!projectName.value.trim()) {
			Error(t("error"), t("project-name-cannot-be-empty"));
			return;
		}

		const saveData = currentSavedData.value
			? JSON.stringify(currentSavedData.value)
			: "{}";
		let request: Project = {
			uuid: currentProjectID.value,
			name: projectName.value.trim(),
			description: projectDesc.value.trim(),
			data: saveData,
			created_at: "",
		};
		if (currentProjectID.value === "") {
			saveLoading.value = true;
			createProject(request)
				.then((uuid) => {
					currentProjectID.value = uuid;
					Success(t("success"), t("save-success"));
					needSave.value = false;
					saveModel.value = false;
					router.push({ name: "workflow", params: { id: uuid } });
				})
				.catch((err) => {
					Error(t("save-error"), err);
				})
				.finally(() => {
					saveLoading.value = false;
				});
			return;
		} else {
			saveLoading.value = true;
			updateProject(currentProjectID.value, request)
				.then(() => {
					Success(t("success"), t("save-success"));
					needSave.value = false;
					saveModel.value = false;
				})
				.catch((err) => {
					Error(t("save-error"), err);
				})
				.finally(() => {
					saveLoading.value = false;
				});
		}
	};

	const alertLeave = (event: any) => {
		if (needSave.value) {
			var message = t("content-not-saved-leave");
			event.preventDefault();
			event.returnValue = "";
			// remove beforeunload listener
			return message;
		}
	};
	window.addEventListener("beforeunload", alertLeave);
	onBeforeRouteLeave((to, from) => {
		if (needSave.value) {
			const answer = window.confirm(t("content-not-saved-leave"));
			if (!answer) return false;
		}
		// 清理事件监听器和重置状态
		window.removeEventListener("beforeunload", alertLeave);
		isInit.value = false;
		needSave.value = false;
	});
	onBeforeRouteUpdate((to, from) => {
		if (needSave.value) {
			const answer = window.confirm(t("content-not-saved-leave"));
			if (!answer) return false;
		}
		// 清理事件监听器和重置状态
		window.removeEventListener("beforeunload", alertLeave);
		isInit.value = false;
		needSave.value = false;
	});

	const data = ref<FlowExportObject>();

	const route = useRoute();

	const init = (id: string, example: string = "") => {
		loadLoading.value = true;
		if (id == "new") {
			// 新建时重置所有状态
			currentProjectID.value = "";
			data.value = {} as FlowExportObject;
			projectName.value = "";
			projectDesc.value = "";
			needSave.value = false;
			loadLoading.value = false;
			if (example) {
				// 根据当前语言判断是否添加"_En"后缀
				const isEnglish = locale.value === "en";
				const exampleKey = example as keyof typeof workflowExample;
				const exampleKeyEn = `${example}_En` as keyof typeof workflowExample;

				// 如果是英文并且存在对应的英文示例，则使用英文示例
				if (isEnglish && workflowExample[exampleKeyEn]) {
					data.value = workflowExample[exampleKeyEn] as FlowExportObject;
				} else {
					// 否则使用原始示例
					data.value = workflowExample[exampleKey] as FlowExportObject;
				}
			}
		} else {
			currentProjectID.value = id;
			loadWorkflow(currentProjectID.value);
		}
		isInit.value = true;
	};

	const loadWorkflow = (id: string) => {
		getProject(id)
			.then((project) => {
				data.value = JSON.parse(project.data || "{}");
				projectName.value = project.name;
				projectDesc.value = project.description;
				isPublished.value = project.is_published || false;
				loadLoading.value = false;
			})
			.catch((err) => {
				Error(t("loading-error"), err);
				loadLoading.value = false;
			});
	};

	init(route.params.id as string, route.query.example as string);
	watch(
		() => route.params,
		(val) => {
			init(val.id as string, route.query.example as string);
		}
	);

	const change = () => {
		if (isInit.value) {
			needSave.value = false;
			isInit.value = false;
			return;
		}
		needSave.value = true;
	};

	// 新增 Config 相关状态管理
	const showConfig = ref(false);
	const configNodeID = ref("");
	const configNodeType = ref("");
	const configWorkflow = ref(flowId.value);

	const closeConfig = () => {
		showConfig.value = false;
		workflowRef.value?.focus();
	};

	const onNodeConfig = (id: string, type: string, subWorkflow?: any) => {
		configNodeID.value = id;
		configNodeType.value = type;
		configWorkflow.value = flowId.value;

		// 如果是循环节点，需要设置子工作流数据
		if (subWorkflow) {
			configWorkflow.value = subWorkflow;
		}

		showConfig.value = true;
	};

	const deleteNode = (id: string) => {
		// 通过 ref 调用 workflow 组件的方法
		workflowRef.value?.deleteNode(id);
	};

	const workflowRef = ref();

	// 运行时相关
	const runtimeData = ref<FlowExportObject>({
		nodes: [],
		edges: [],
		position: [0, 0],
		zoom: 1,
		viewport: { x: 0, y: 0, zoom: 1 },
	});
	const runtimeShow = ref(false);

	const handleRun = () => {
		const flowObject = workflowRef.value?.run();
		if (!flowObject) return;

		runtimeShow.value = true;
		runtimeData.value = flowObject;
	};

	const { options: nodeOptions } = useNodeOptions();

	const handlePublish = () => {
		// 先获取最新的工作流数据
		const { flowObject } = workflowRef.value?.save() || {};
		if (flowObject) {
			currentSavedData.value = flowObject;
		}
		showPublishModal.value = true;
	};

	const handlePublishSuccess = () => {
		isPublished.value = true;
	};
</script>

<template>
	<div class="w-full h-full">
		<!-- Loading 状态 -->
		<div
			class="w-full h-full flex justify-center items-center flex-col"
			v-if="loadLoading"
		>
			<span class="loading loading-ring loading-lg"></span>
			<span class="text-md text-center mt-3 text-gray-500">{{ t("loading") }}</span>
		</div>

		<!-- 主要内容 -->
		<div class="relative h-full" v-else>
			<!-- 控制按钮 -->
			<div
				class="absolute top-2 left-2 z-50 bg-[#fafafa] rounded-xl p-2 shadow-md bg-opacity-65"
			>
				<button
					class="btn inline-flex items-center btn-sm"
					@click="router.push({ name: 'projects' })"
				>
					<ArrowLeftIcon class="w-4 h-4" />
					{{ t("return") }}
				</button>
				<button
					class="btn btn-secondary ml-2 inline-flex items-center btn-sm"
					@click="handleRun"
				>
					<PlayIcon class="w-4 h-4" />
					{{ t("debug") }}
				</button>
				<button
					class="btn btn-primary ml-2 inline-flex items-center btn-sm"
					@click="onWorkflowSave"
				>
					<BookmarkIcon class="w-4 h-4" />
					{{ t("save") }}
				</button>
				<button
					v-if="currentProjectID"
					class="btn btn-neutral ml-2 inline-flex items-center btn-sm"
					@click="handlePublish"
				>
					<CodeBracketIcon class="w-4 h-4" />
					{{ t("publish_api") }}
				</button>
			</div>

			<!-- Workflow 组件 -->
			<component_wait>
				<Workflow
					ref="workflowRef"
					:flowId="flowId"
					class="bg-base-100 p-4 rounded-lg h-full"
					@change="change"
					@node-config="onNodeConfig"
					:data="data"
					:key="currentProjectID"
					:options="nodeOptions"
				/>
			</component_wait>

			<!-- Runtime 组件 -->
			<Teleport to="body">
				<Runtime v-model="runtimeShow" :data="runtimeData" />
			</Teleport>

			<!-- Config 组件 -->
			<Config
				v-if="showConfig"
				@close="closeConfig"
				@delete="deleteNode"
				:id="configNodeID"
				:type="configNodeType"
				:flowId="configWorkflow"
			/>

			<!-- 保存对话框 -->
			<Teleport to="body">
				<div class="modal" :class="{ 'modal-open': saveModel }">
					<div
						class="modal-box relative md:max-w-[80%] md:w-[80%] lg:max-w-[60%] lg:w-[60%] max-w-full w-full"
					>
						<h3 class="font-bold text-xl">{{ t("save") }}</h3>
						<label>{{ t("project-name") }}</label>
						<input
							class="input input-bordered w-full input-md"
							v-model="projectName"
						/>
						<label>{{ t("project-description") }}</label>
						<textarea
							class="textarea textarea-bordered w-full"
							v-model="projectDesc"
						></textarea>
						<div class="modal-action">
							<button class="btn" @click="saveModel = false">
								{{ t("close") }}
							</button>
							<button
								class="btn btn-primary"
								:class="{ 'btn-disabled': saveLoading }"
								@click="workflowSaveProcess"
							>
								<span
									v-if="saveLoading"
									class="loading loading-spinner loading-sm"
								></span>
								{{ t("save") }}
							</button>
						</div>
					</div>

					<label class="modal-backdrop" @click="saveModel = false"></label>
				</div>
			</Teleport>

			<!-- 发布对话框 -->
			<PublishModal
				v-model="showPublishModal"
				:workflow-id="currentProjectID"
				:project-name="projectName"
				:project-desc="projectDesc"
				:is-published="isPublished"
				:flow-data="currentSavedData"
				@publish-success="handlePublishSuccess"
				@save-success="needSave = false"
			/>
		</div>
	</div>
</template>

<style></style>
