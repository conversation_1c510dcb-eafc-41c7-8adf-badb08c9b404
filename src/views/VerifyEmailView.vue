<template>
	<div class="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12">
		<div class="absolute top-4 right-4">
			<select
				@change="(e) => changeLanguage((e.target as HTMLSelectElement).value)"
				class="select select-ghost select-xs"
			>
				<option value="en" :selected="locale === 'en'">EN</option>
				<option value="zh" :selected="locale === 'zh'">中</option>
			</select>
		</div>
		<div class="relative py-3 sm:max-w-xl sm:mx-auto w-full px-4 sm:px-0">
			<div
				class="absolute inset-0 bg-gradient-to-r from-primary to-secondary shadow-lg transform -skew-y-6 sm:skew-y-0 sm:-rotate-6 sm:rounded-3xl"
			></div>
			<div class="relative px-4 py-10 bg-white shadow-lg rounded-3xl sm:p-20">
				<div class="max-w-md mx-auto">
					<div class="flex items-center gap-2 justify-center sm:justify-start">
						<router-link to="/" class="flex items-center gap-2">
							<img src="/main_logo.png" class="w-40 max-sm:w-32" />
						</router-link>
					</div>
					<div class="mt-6">
						<div class="text-center mb-4">
							<h2 class="text-2xl font-bold text-gray-800">
								{{ t("email_verification") }}
							</h2>
						</div>

						<div class="p-3 bg-blue-50 rounded-lg mb-6 text-sm">
							<div class="flex items-center text-blue-800">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-5 w-5 mr-2"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
									/>
								</svg>
								<span
									>{{ t("verification_code_sent_to") }}
									<strong>{{ email }}</strong></span
								>
							</div>
							<div
								v-if="autoVerifying"
								class="mt-2 text-blue-800 flex items-center"
							>
								<span class="loading loading-spinner loading-xs mr-2"></span>
								<span>{{ t("auto_verifying") }}</span>
							</div>
						</div>

						<div @keydown.enter.prevent="verifyEmail">
							<div
								class="py-4 text-base leading-6 space-y-4 text-gray-700 sm:text-lg sm:leading-7"
							>
								<div class="flex flex-col">
									<label for="verificationCode" class="font-semibold">{{
										t("verification_code")
									}}</label>
									<input
										id="verificationCode"
										type="text"
										v-model="verificationCode"
										class="input input-bordered w-full mt-2"
										:placeholder="t('enter_verification_code')"
									/>
								</div>
							</div>
							<div
								class="pt-4 text-base leading-6 font-bold sm:text-lg text-center"
							>
								<span
									v-if="loading"
									class="loading loading-ring loading-lg"
								></span>
								<button
									v-if="!loading"
									class="btn btn-primary w-full"
									@click="verifyEmail"
								>
									{{ t("verify_email") }}
								</button>
							</div>
							<div class="pt-4 text-center">
								<p class="text-sm text-gray-600">
									{{ t("didnt_receive_code") }}
									<a
										href="#"
										class="text-primary hover:underline"
										@click.prevent="resendCode"
									>
										{{ t("resend_code") }}
									</a>
								</p>
								<p class="text-sm text-gray-600 mt-2">
									<router-link
										to="/login"
										class="text-primary hover:underline"
									>
										{{ t("back_to_login") }}
									</router-link>
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { verifyEmail as verifyEmailAPI, resendVerifyCode } from "@/api/user";
	import { Error, Success } from "@/utils/notify";
	import { ref, onMounted } from "vue";
	import { useRouter, useRoute } from "vue-router";
	import { useI18n } from "vue-i18n";
	import { useUserStore } from "@/stores/user";

	const { t, locale } = useI18n();
	const loading = ref(false);
	const autoVerifying = ref(false); // Flag for auto-verification
	const verificationCode = ref("");
	const email = ref("");
	const router = useRouter();
	const route = useRoute();
	const userStore = useUserStore();

	onMounted(() => {
		// 从URL查询参数获取邮箱地址和验证码
		const queryEmail = route.query.email as string;
		const queryCode = route.query.code as string;

		// 优先使用查询参数中的邮箱，其次使用路由参数中的邮箱
		if (queryEmail) {
			email.value = queryEmail;
		} else if (route.params.email) {
			email.value = route.params.email as string;
		} else {
			// 如果没有邮箱参数，重定向到注册页面
			router.push({ name: "register" });
			return;
		}

		// 如果URL中包含验证码，自动填入
		if (queryCode) {
			verificationCode.value = queryCode;
			// 如果同时有邮箱和验证码，自动提交验证
			if (email.value) {
				autoVerifying.value = true; // Set auto-verification flag
				// 小延时以显示自动验证消息
				setTimeout(() => {
					verifyEmail();
				}, 1000);
			}
		}
	});

	function verifyEmail() {
		if (!verificationCode.value) {
			Error(t("error"), t("please_enter_verification_code"));
			autoVerifying.value = false;
			return;
		}

		loading.value = true;
		verifyEmailAPI(email.value, verificationCode.value)
			.then((res) => {
				Success(t("verification_success"), t("email_verified"));
				// 登录并重定向到首页
				userStore.setUser(res.data);
				router.push({ name: "dashboard" });
			})
			.catch((err) => {
				Error(t("verification_error"), err);
				autoVerifying.value = false;
			})
			.finally(() => {
				loading.value = false;
			});
	}

	function resendCode() {
		loading.value = true;
		// 重新发送验证码
		resendVerifyCode(email.value, locale.value)
			.then(() => {
				Success(t("resend_success"), t("verification_email_sent"));
			})
			.catch((err: any) => {
				Error(t("resend_error"), err);
			})
			.finally(() => {
				loading.value = false;
			});
	}

	const changeLanguage = (lang: string) => {
		locale.value = lang;
		localStorage.setItem("locale", lang);
	};
</script>
