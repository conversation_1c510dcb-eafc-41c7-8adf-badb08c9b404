<script setup lang="ts">
	import { type Project, getProjects, deleteProject, cloneProject } from "@/api/projects";
	import { useCurNav } from "@/stores/curNav";
	import { ref, onMounted, onBeforeUnmount, watch, defineAsyncComponent } from "vue";
	import { useRouter } from "vue-router";
	import {
		RocketLaunchIcon,
		PencilSquareIcon,
		TrashIcon,
		DocumentDuplicateIcon,
		DocumentPlusIcon,
	} from "@heroicons/vue/24/outline";
	import { EllipsisVerticalIcon } from "@heroicons/vue/20/solid";
	import { useI18n } from "vue-i18n";
	import { Error } from "@/utils/notify";
	import DeleteConfirmDialog from "@/components/common/DeleteConfirmDialog.vue";
	const { t } = useI18n();
	const Page = defineAsyncComponent(() => import("@/components/utils/page.vue"));
	const curNavStore = useCurNav();
	curNavStore.setCurNav("projects");

	type myProject = {
		showDropdown: boolean;
		dropdownPosition: { top: number; left: number };
	} & Project;

	const projects = ref<myProject[]>([]);
	const total = ref(0);
	const page = ref(1);
	const pageSize = ref(16);
	const loading = ref(false);

	// 删除确认对话框状态
	const showDeleteDialog = ref(false);
	const deleteTarget = ref<myProject | null>(null);
	const isDeleting = ref(false);
	const load = () => {
		loading.value = true;
		getProjects(page.value, pageSize.value)
			.then((res) => {
				projects.value = res.list.map((project: Project) => ({
					...project,
					showDropdown: false,
					dropdownPosition: { top: 0, left: 0 },
				}));
				total.value = res.total;
			})
			.finally(() => {
				loading.value = false;
			});
	};
	watch([page, pageSize], load);
	load();

	const toggleDropdown = (uuid: string, event: MouseEvent) => {
		event.stopPropagation();
		const project = projects.value.find((p) => p.uuid === uuid);
		if (project) {
			project.showDropdown = !project.showDropdown;
			project.dropdownPosition = { top: event.clientY, left: event.clientX };
		}
	};

	const handleClickOutside = () => {
		projects.value.forEach((project) => {
			if (project.showDropdown) {
				project.showDropdown = false;
			}
		});
	};

	onMounted(() => {
		document.addEventListener("click", handleClickOutside);
	});

	onBeforeUnmount(() => {
		document.removeEventListener("click", handleClickOutside);
	});

	let options = [
		{
			label: t("create-copy"),
			icon: DocumentDuplicateIcon,
			action: (project: myProject) => {
				cloneProject(project.uuid)
					.then(() => {
						load();
					})
					.catch((error) => {
						console.error("克隆项目失败:", error);
						Error(t("create-copy-failed"), error || t("operation-failed"));
					});
			},
		},
		{
			label: t("delete"),
			icon: TrashIcon,
			action: (project: myProject) => {
				deleteTarget.value = project;
				showDeleteDialog.value = true;
			},
		},
	];

	const router = useRouter();

	const createNewWorkflow = (example: string = "") => {
		router.push({
			name: "workflow",
			params: { id: "new" },
			query: { example },
		});
	};

	// 删除确认对话框相关函数
	const closeDeleteDialog = () => {
		showDeleteDialog.value = false;
		deleteTarget.value = null;
	};

	const confirmDeleteProject = async () => {
		if (!deleteTarget.value) return;

		isDeleting.value = true;
		try {
			await deleteProject(deleteTarget.value.uuid);
			load();
			closeDeleteDialog();
		} catch (error) {
			console.error("删除项目失败:", error);
			Error(t("delete-failed"), t("operation-failed"));
		} finally {
			isDeleting.value = false;
		}
	};
</script>

<template>
	<div class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-full p-6">
		<!-- 加载状态 -->
		<div
			v-if="loading"
			class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
		>
			<div
				v-for="i in 12"
				:key="i"
				class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 animate-pulse border border-slate-200"
			>
				<div class="h-5 bg-slate-200 rounded w-3/4 mb-4"></div>
				<div class="h-4 bg-slate-200 rounded w-1/2 mb-3"></div>
				<div class="h-4 bg-slate-200 rounded w-1/4 mb-4"></div>
				<div class="flex justify-between items-center">
					<div class="h-8 bg-slate-200 rounded w-8"></div>
					<div class="flex gap-2">
						<div class="h-8 bg-slate-200 rounded w-16"></div>
						<div class="h-8 bg-slate-200 rounded w-16"></div>
					</div>
				</div>
			</div>
		</div>
		<!-- 空状态 -->
		<div v-else-if="projects.length === 0" class="text-center py-16">
			<div class="max-w-4xl mx-auto">
				<div
					class="bg-white/80 backdrop-blur-sm rounded-3xl border border-slate-200 shadow-xl p-12"
				>
					<img src="@/assets/empty-projects.svg" class="mx-auto w-48 mb-8" />
					<div class="space-y-8">
						<div>
							<h2 class="text-3xl font-bold text-slate-800 mb-4">
								{{ t("no-projects") }}
							</h2>
							<p class="text-lg text-slate-600">
								{{ t("click-button-to-start-automation") }}
							</p>
						</div>
						<div>
							<button
								@click="createNewWorkflow()"
								class="btn-gradient-primary btn-lg gap-3"
							>
								<DocumentPlusIcon class="w-5 h-5" />
								{{ t("create-blank-workflow") }}
							</button>
						</div>
						<div class="border-t border-slate-200 pt-8 mt-8">
							<p class="text-lg text-slate-600 mb-8">
								{{ t("or-start-from-example") }}
							</p>
							<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
								<div
									class="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 py-6 px-4 border border-slate-200 hover:border-blue-300 hover:-translate-y-1 relative group"
								>
									<div
										class="absolute -top-3 left-4 px-3 py-1 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 text-xs rounded-full font-medium border border-blue-300"
									>
										{{ t("example") }}
									</div>
									<div class="flex items-center mb-3">
										<div
											class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center mr-3"
										>
											<DocumentDuplicateIcon
												class="w-5 h-5 text-blue-600"
											/>
										</div>
										<h3
											class="text-lg font-bold text-slate-800 text-left break-all group-hover:text-blue-600 transition-colors"
										>
											{{ t("llm-data-processing") }}
										</h3>
									</div>
									<p
										class="text-sm text-slate-600 mb-4 text-left leading-relaxed"
									>
										{{ t("llm-data-processing-description") }}
									</p>
									<button
										@click="createNewWorkflow('llmDataProcessing')"
										class="btn-gradient-primary btn-sm w-full"
									>
										{{ t("use-this-example") }}
									</button>
								</div>

								<div
									class="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 py-6 px-4 border border-slate-200 hover:border-green-300 hover:-translate-y-1 relative group"
								>
									<div
										class="absolute -top-3 left-4 px-3 py-1 bg-gradient-to-r from-green-100 to-green-200 text-green-800 text-xs rounded-full font-medium border border-green-300"
									>
										{{ t("example") }}
									</div>
									<div class="flex items-center mb-3">
										<div
											class="w-10 h-10 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center mr-3"
										>
											<RocketLaunchIcon class="w-5 h-5 text-green-600" />
										</div>
										<h3
											class="text-lg font-bold text-slate-800 text-left break-all group-hover:text-green-600 transition-colors"
										>
											{{ t("multi-llm-assistant") }}
										</h3>
									</div>
									<p
										class="text-sm text-slate-600 mb-4 text-left leading-relaxed"
									>
										{{ t("integrate-multiple-llm-models") }}
									</p>
									<button
										@click="createNewWorkflow('multiLlmAssistant')"
										class="btn-gradient-success btn-sm w-full"
									>
										{{ t("use-this-example") }}
									</button>
								</div>

								<div
									class="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 py-6 px-4 border border-slate-200 hover:border-purple-300 hover:-translate-y-1 relative group"
								>
									<div
										class="absolute -top-3 left-4 px-3 py-1 bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 text-xs rounded-full font-medium border border-purple-300"
									>
										{{ t("example") }}
									</div>
									<div class="flex items-center mb-3">
										<div
											class="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center mr-3"
										>
											<DocumentPlusIcon
												class="w-5 h-5 text-purple-600"
											/>
										</div>
										<h3
											class="text-lg font-bold text-slate-800 text-left break-all group-hover:text-purple-600 transition-colors"
										>
											{{ t("logic-flow") }}
										</h3>
									</div>
									<p
										class="text-sm text-slate-600 mb-4 text-left leading-relaxed"
									>
										{{ t("visual-logic-flow") }}
									</p>
									<button
										@click="createNewWorkflow('logicFlow')"
										class="btn-gradient-purple btn-sm w-full"
									>
										{{ t("use-this-example") }}
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 项目列表 -->
		<div
			v-else
			class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
		>
			<div
				v-for="i in projects"
				:key="i.uuid"
				class="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-200 hover:border-blue-300 overflow-hidden"
				:data-project-id="i.uuid"
			>
				<div class="p-6">
					<div class="flex justify-between items-start">
						<div class="flex-1">
							<h2
								class="text-xl font-bold text-slate-800 mb-2 group-hover:text-blue-600 transition-colors leading-tight"
							>
								{{ i.name || t("unnamed-project") }}
							</h2>
						</div>
					</div>

					<p class="text-sm text-slate-600 mb-4 min-h-[3em] leading-relaxed">
						{{ i.description || t("no-description") }}
					</p>

					<div class="flex items-center text-xs text-slate-500 mb-6">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="h-4 w-4 mr-2 text-blue-500"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
							/>
						</svg>
						<span class="font-medium">
							{{ new Date(i.created_at).toLocaleDateString() }}
							{{ new Date(i.created_at).toLocaleTimeString() }}
						</span>
					</div>
					<div class="flex justify-between items-center">
						<div class="relative">
							<button
								class="text-slate-500 focus:outline-none bg-slate-100 hover:bg-slate-200 p-2 rounded-xl transition-all duration-300 hover:text-slate-700"
								@click="toggleDropdown(i.uuid, $event)"
							>
								<EllipsisVerticalIcon class="w-5 h-5" />
							</button>
							<Teleport to="body" v-if="i.showDropdown">
								<div
									v-motion-pop
									class="absolute z-10 mt-2 w-48 rounded-xl shadow-xl bg-white/95 backdrop-blur-sm border border-slate-200"
									:style="{
										top: i.dropdownPosition.top + 'px',
										left: i.dropdownPosition.left + 'px',
									}"
								>
									<div class="py-2">
										<a
											v-for="option in options"
											:key="option.label"
											@click="option.action(i)"
											class="flex items-center px-4 py-3 text-sm text-slate-700 hover:bg-slate-100 cursor-pointer transition-colors"
										>
											<component
												:is="option.icon"
												class="w-4 h-4 mr-3"
												:class="{
													'text-blue-500':
														option.label === t('create-copy'),
													'text-red-500':
														option.label === t('delete'),
												}"
											/>
											<span class="font-medium">{{ option.label }}</span>
										</a>
									</div>
								</div>
							</Teleport>
						</div>
						<div class="flex gap-3">
							<RouterLink
								:to="{ name: 'runtime', params: { id: i.uuid } }"
								class="btn-outline-primary btn-sm gap-2"
							>
								<RocketLaunchIcon class="w-4 h-4" />
								{{ t("run") }}
							</RouterLink>
							<RouterLink
								:to="{ name: 'workflow', params: { id: i.uuid } }"
								class="btn-gradient-primary btn-sm gap-2"
							>
								<PencilSquareIcon class="w-4 h-4" />
								{{ t("edit") }}
							</RouterLink>
						</div>
					</div>
				</div>
			</div>
		</div>
		<Page
			v-if="projects.length > 0"
			:page="page"
			:pageSize="pageSize"
			:total="total"
			@change="(e) => (page = e)"
			class="mt-8"
		></Page>

		<!-- 删除确认对话框 -->
		<DeleteConfirmDialog
			:show="showDeleteDialog"
			:item-name="deleteTarget?.name"
			:is-loading="isDeleting"
			@close="closeDeleteDialog"
			@confirm="confirmDeleteProject"
		/>
	</div>
</template>
