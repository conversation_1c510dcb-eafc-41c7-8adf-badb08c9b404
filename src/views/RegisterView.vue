<template>
	<div class="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12">
		<div class="absolute top-4 right-4">
			<select
				@change="(e) => changeLanguage((e.target as HTMLSelectElement).value)"
				class="select select-ghost select-xs"
			>
				<option value="en" :selected="locale === 'en'">EN</option>
				<option value="zh" :selected="locale === 'zh'">中</option>
			</select>
		</div>
		<div class="relative py-3 sm:max-w-xl sm:mx-auto w-full px-4 sm:px-0">
			<div
				class="absolute inset-0 bg-gradient-to-r from-primary to-secondary shadow-lg transform -skew-y-6 sm:skew-y-0 sm:-rotate-6 sm:rounded-3xl"
			></div>
			<div class="relative px-4 py-10 bg-white shadow-lg rounded-3xl sm:p-20">
				<div class="max-w-md mx-auto">
					<div class="flex items-center gap-2 justify-center sm:justify-start">
						<router-link to="/" class="flex items-center gap-2">
							<img src="/main_logo.png" class="w-40 max-sm:w-32" />
						</router-link>
					</div>
					<div class="mt-6">
						<div class="text-center mb-4">
							<h2 class="text-2xl font-bold text-gray-800">{{ t("signup") }}</h2>
						</div>

						<!-- 积分奖励提示 -->
						<div class="p-3 bg-blue-50 rounded-lg mb-6 text-sm">
							<div class="flex items-center text-blue-800 mb-1">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-5 w-5 mr-2"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
									/>
								</svg>
								<span class="font-semibold">{{
									t("credits_reward_info")
								}}</span>
							</div>
							<ul class="list-disc list-inside pl-2 text-blue-700">
								<li>{{ t("email_signup_credits", { credits: 5 }) }}</li>
								<li>{{ t("oauth_signup_credits", { credits: 20 }) }}</li>
							</ul>
						</div>

						<div @keydown.enter.prevent="register">
							<div
								class="py-4 text-base leading-6 space-y-4 text-gray-700 sm:text-lg sm:leading-7"
							>
								<div class="flex flex-col">
									<label for="email" class="font-semibold">{{
										t("email")
									}}</label>
									<input
										id="email"
										type="email"
										v-model="email"
										class="input input-bordered w-full mt-2"
										:placeholder="t('enter_email')"
									/>
								</div>
								<div class="flex flex-col pt-4">
									<label for="password" class="font-semibold">{{
										t("login_password")
									}}</label>
									<input
										id="password"
										type="password"
										v-model="password"
										class="input input-bordered w-full mt-2"
									/>
								</div>
								<div class="flex flex-col pt-4">
									<label for="confirmPassword" class="font-semibold">{{
										t("confirm_password")
									}}</label>
									<input
										id="confirmPassword"
										type="password"
										v-model="confirmPassword"
										class="input input-bordered w-full mt-2"
									/>
								</div>
							</div>
							<div
								class="pt-4 text-base leading-6 font-bold sm:text-lg text-center"
							>
								<span
									v-if="loading"
									class="loading loading-ring loading-lg"
								></span>
								<button
									v-if="!loading"
									class="btn btn-primary w-full"
									@click="register"
								>
									{{ t("signup") }}
								</button>
								<div v-if="!loading" class="divider">{{ t("or") }}</div>
								<button
									v-if="!loading"
									class="btn btn-outline w-full flex items-center justify-center gap-2"
									@click="googleLogin"
								>
									<img
										src="@/assets/google.svg"
										alt="Google Logo"
										width="18"
										height="18"
									/>
									{{ t("signup_with_google") }}
								</button>
								<button
									v-if="!loading"
									class="btn btn-outline w-full flex items-center justify-center gap-2 mt-4"
									@click="githubLogin"
								>
									<img
										src="@/assets/github.svg"
										alt="Github Logo"
										width="18"
										height="18"
									/>
									{{ t("signup_with_github") }}
								</button>
							</div>
							<div class="pt-4 text-center">
								<p class="text-sm text-gray-600">
									{{ t("login_msg") }}
									<router-link
										to="/login"
										class="text-primary hover:underline"
									>
										{{ t("login") }}
									</router-link>
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import {
		signUp,
		googleLogin as googleLoginAPI,
		githubLogin as githubLoginAPI,
	} from "@/api/user";
	import { Error, Success } from "@/utils/notify";
	import { ref } from "vue";
	import { useRouter } from "vue-router";
	import { useI18n } from "vue-i18n";

	const { t, locale } = useI18n();
	const loading = ref(false);
	const email = ref("");
	const password = ref("");
	const confirmPassword = ref("");
	const router = useRouter();

	function register() {
		if (!email.value || !password.value || !confirmPassword.value) {
			Error(t("error"), t("please_fill_all_fields"));
			return;
		}

		if (password.value !== confirmPassword.value) {
			Error(t("error"), t("password_not_match"));
			return;
		}

		// 简单的邮箱验证
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email.value)) {
			Error(t("error"), t("invalid_email"));
			return;
		}

		loading.value = true;
		signUp(email.value, password.value, locale.value)
			.then(() => {
				Success(t("register_success"), t("verification_email_sent"));
				router.push({ name: "verify-email", params: { email: email.value } });
			})
			.catch((err) => {
				Error(t("register_error"), err);
			})
			.finally(() => {
				loading.value = false;
			});
	}

	function googleLogin() {
		loading.value = true;
		googleLoginAPI()
			.then((res: any) => {
				window.location.href = res.url;
			})
			.catch((err) => {
				Error(t("google-login-error"), err);
			})
			.finally(() => {
				loading.value = false;
			});
	}

	function githubLogin() {
		loading.value = true;
		githubLoginAPI()
			.then((res: any) => {
				window.location.href = res.url;
			})
			.catch((err) => {
				Error(t("github-login-error"), err);
			})
			.finally(() => {
				loading.value = false;
			});
	}

	const changeLanguage = (lang: string) => {
		locale.value = lang;
		localStorage.setItem("locale", lang);
	};
</script>
