<script setup lang="ts">
	import { useCurNav } from "@/stores/curNav";
	import { ref, defineAsyncComponent, onMounted, onUnmounted, nextTick } from "vue";
	import component_wait from "@/components/utils/component_wait.vue";
	import { useI18n } from "vue-i18n";

	const PersonalSettings = defineAsyncComponent(
		() => import("@/components/setting/personal.vue")
	);
	const CustomLLM = defineAsyncComponent(() => import("@/components/setting/llm.vue"));

	const curNavStore = useCurNav();
	curNavStore.setCurNav("setting");

	const { t } = useI18n();

	const sections = [
		{ id: "personal", name: t("personal_settings") },
		{ id: "custom-llm", name: t("custom_llm") },
	];

	const activeSection = ref("personal");

	const scrollToSection = (sectionId: string) => {
		activeSection.value = sectionId;
		const element = document.getElementById(sectionId);
		if (element) {
			element.scrollIntoView({ behavior: "smooth", block: "start", inline: "nearest" });
		}
	};

	// 添加 Intersection Observer 相关逻辑
	let observers: IntersectionObserver[] = [];

	const setupIntersectionObserver = () => {
		// 先清理现有的观察器
		observers.forEach((observer) => observer.disconnect());
		observers = [];

		// 创建新的观察器
		const options = {
			root: null, // 使用视口作为根
			threshold: [0.1, 0.5, 0.9], // 多个阈值以提高精确度
			rootMargin: "-100px 0px -300px 0px", // 调整观察区域
		};

		const observer = new IntersectionObserver((entries) => {
			entries.forEach((entry) => {
				// 当元素进入视口且可见度超过50%时
				if (entry.isIntersecting && entry.intersectionRatio > 0.2) {
					const sectionId = entry.target.id;
					if (activeSection.value !== sectionId) {
						activeSection.value = sectionId;
					}
				}
			});
		}, options);

		// 观察所有section
		sections.forEach((section) => {
			const element = document.getElementById(section.id);
			if (element) {
				observer.observe(element);
				observers.push(observer);
			}
		});
	};

	onMounted(() => {
		// 等待DOM完全渲染后再设置观察器
		nextTick(() => {
			setupIntersectionObserver();
		});
	});

	onUnmounted(() => {
		observers.forEach((observer) => observer.disconnect());
		observers = [];
	});
</script>

<template>
	<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex flex-col">
		<!-- 粘性导航栏 -->
		<div class="sticky top-0 bg-white shadow-md z-10 lg:hidden">
			<div class="max-w-6xl mx-auto px-4 sm:px-6">
				<ul class="flex overflow-x-auto py-3 space-x-4">
					<li v-for="section in sections" :key="section.id" class="flex-shrink-0">
						<button
							@click="scrollToSection(section.id)"
							:class="[
								'px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200',
								activeSection === section.id
									? 'bg-primary text-white'
									: 'bg-gray-200 text-gray-700 hover:bg-gray-300',
							]"
						>
							{{ section.name }}
						</button>
					</li>
				</ul>
			</div>
		</div>

		<!-- 主要内容 -->
		<div class="flex-grow py-6 px-4 sm:py-12 sm:px-6 lg:px-8 lg:mt-0">
			<div class="max-w-6xl mx-auto lg:flex">
				<!-- 左侧导航（仅在大屏幕显示） -->
				<div class="hidden lg:block lg:w-64 lg:mr-8">
					<div class="bg-white shadow-md rounded-lg overflow-hidden sticky top-4">
						<ul class="p-4">
							<li
								v-for="section in sections"
								:key="section.id"
								class="w-full mb-2 last:mb-0"
							>
								<button
									@click="scrollToSection(section.id)"
									:class="[
										'w-full text-left px-4 py-2 rounded-md',
										activeSection === section.id
											? 'bg-primary text-white'
											: 'hover:bg-gray-100',
									]"
								>
									{{ section.name }}
								</button>
							</li>
						</ul>
					</div>
				</div>

				<!-- 右侧内容 -->
				<div class="flex-1">
					<div class="bg-white shadow-md rounded-lg overflow-hidden">
						<div class="p-6 border-b border-gray-200">
							<h1 class="text-2xl font-bold text-gray-900">
								{{ t("settings") }}
							</h1>
						</div>

						<div class="p-4 sm:p-6">
							<!-- 个人设置表单 -->
							<div id="personal" class="mb-6">
								<h2 class="text-xl font-bold text-gray-900 mb-4">
									{{ t("personal_settings") }}
								</h2>
								<component_wait>
									<PersonalSettings />
								</component_wait>
							</div>

							<!-- 自定义LLM配置部分 -->
							<div id="custom-llm" class="border-t border-gray-200 pt-6">
								<h2 class="text-xl font-bold text-gray-900 mb-4">
									{{ t("custom_llm") }}
								</h2>
								<component_wait>
									<CustomLLM />
								</component_wait>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
