<script setup lang="ts">
	import { useCurNav } from "@/stores/curNav";
	import {
		type WorkflowLog,
		getWorkflowLog,
		getWorkflowLogs,
		deleteWorkflowLog,
	} from "@/api/logs";
	import { ref, watch, onMounted, defineAsyncComponent, nextTick } from "vue";
	import { Error } from "@/utils/notify";
	import {
		TrashIcon,
		EyeIcon,
		EyeSlashIcon,
		ArrowsPointingOutIcon,
	} from "@heroicons/vue/24/outline";
	import DeleteConfirmDialog from "@/components/common/DeleteConfirmDialog.vue";
	import component_wait from "@/components/utils/component_wait.vue";
	import LogContent from "@/components/LogContent.vue";

	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	// 延迟加载组件
	const Page = defineAsyncComponent(() => import("@/components/utils/page.vue"));

	const curNavStore = useCurNav();
	curNavStore.setCurNav("run_logs");

	const logs = ref<WorkflowLog[]>([]);
	const logCount = ref(0);
	const page = ref(1);
	const pageSize = ref(10);
	const loading = ref(true);
	const getLoading = ref<string | null>(null);
	const opened = ref<string[]>([]);
	const fullscreenLogId = ref<string | null>(null);
	const logListRef = ref<HTMLElement | null>(null);

	const deletingLogs = ref(new Set<string>());

	// 删除确认对话框状态
	const showDeleteDialog = ref(false);
	const deleteTarget = ref<string | null>(null);
	const isDeleting = ref(false);

	const load = () => {
		loading.value = true;
		getWorkflowLogs(page.value, pageSize.value)
			.then((res) => {
				logs.value = res.list;
				logCount.value = res.total;
			})
			.finally(() => {
				loading.value = false;
			});
	};

	const getLog = (uuid: string) => {
		let index = logs.value.findIndex((i) => i.uuid == uuid);
		if (index == -1 || logs.value[index].output !== "") {
			return;
		}

		getLoading.value = uuid;
		getWorkflowLog(uuid)
			.then((res) => {
				logs.value[index] = res;
				let out = JSON.parse(res.output || "{}");
				logs.value[index].output = out.output || out.msg;
				logs.value[index].total_time = out.total_time || 0;
				logs.value[index].error = out.error || "";
			})
			.catch((err) => {
				// Error("获取日志失败", err);
			})
			.finally(() => {
				getLoading.value = null;
			});
	};

	const deleteLog = (uuid: string) => {
		deleteTarget.value = uuid;
		showDeleteDialog.value = true;
	};

	const closeDeleteDialog = () => {
		showDeleteDialog.value = false;
		deleteTarget.value = null;
	};

	const confirmDeleteLog = async () => {
		if (!deleteTarget.value) return;

		const uuid = deleteTarget.value;
		isDeleting.value = true;
		deletingLogs.value.add(uuid);
		closeLog(uuid);

		try {
			await deleteWorkflowLog(uuid);
			logs.value = logs.value.filter((log) => log.uuid !== uuid);
			closeDeleteDialog();
		} catch (err: any) {
			Error(t("delete-log-failed"), err);
		} finally {
			isDeleting.value = false;
			deletingLogs.value.delete(uuid);
		}
	};

	watch(() => page.value, load);

	onMounted(() => {
		loading.value = true;
		load();
	});

	const showLog = (uuid: string) => {
		if (!opened.value.includes(uuid)) {
			getLog(uuid);
			opened.value.push(uuid);
		}
	};

	const closeLog = (uuid: string) => {
		opened.value = opened.value.filter((openId) => openId !== uuid);
	};

	const showFullscreen = (uuid: string) => {
		fullscreenLogId.value = uuid;
		getLog(uuid);
	};

	const closeFullscreen = () => {
		fullscreenLogId.value = null;
		nextTick(() => {
			if (logListRef.value) {
				logListRef.value.focus();
			}
		});
	};
</script>

<template>
	<div>
		<!-- 删除确认对话框 -->
		<DeleteConfirmDialog
			v-if="showDeleteDialog"
			:show="showDeleteDialog"
			:isDeleting="isDeleting"
			:title="t('confirm-delete-log')"
			:confirmText="t('delete')"
			:confirmButtonVariant="'error'"
			@close="closeDeleteDialog"
			@confirm="confirmDeleteLog"
		/>
		<div
			class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen p-6"
			ref="logListRef"
		>
			<div class="flex items-baseline mb-6">
				<h1 class="text-2xl md:text-3xl font-bold text-gray-800">
					{{ t("run-logs") }}
				</h1>
				<span class="ml-3 text-sm text-gray-500">
					{{ t("show-logs-in-last-15-days") }}
				</span>
			</div>

			<!-- 加载中状态 -->
			<div v-if="loading" class="space-y-4">
				<div
					v-for="i in 3"
					:key="i"
					class="bg-white rounded-lg shadow-md p-4 animate-pulse"
				>
					<div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
					<div class="h-4 bg-gray-200 rounded w-1/2"></div>
				</div>
			</div>

			<!-- 日志列表 -->
			<div v-if="logs.length > 0 && !loading" tabindex="-1">
				<transition-group name="slide-fade" tag="div">
					<div
						v-for="log in logs"
						:key="log.uuid"
						:id="'log-' + log.uuid"
						class="rounded-xl shadow-md overflow-hidden mb-2 bg-white hover:shadow-lg duration-300"
					>
						<div class="p-4 flex items-center border-b">
							<div class="flex-grow min-w-0 mr-4">
								<h2
									class="text-lg font-semibold text-gray-800 max-sm:text-sm truncate"
								>
									{{ log.workflow?.name || "未命名" }}
								</h2>
								<p class="text-sm text-gray-500 truncate">
									{{ log.created_at }}
								</p>
							</div>
							<div class="flex-shrink-0 flex space-x-2">
								<button
									@click="showLog(log.uuid)"
									class="btn btn-sm btn-primary whitespace-nowrap"
									v-if="!opened.includes(log.uuid)"
								>
									<EyeIcon class="w-4 h-4" /> {{ t("view") }}
								</button>
								<button
									@click="closeLog(log.uuid)"
									class="btn btn-sm btn-outline btn-primary whitespace-nowrap"
									v-else
								>
									<EyeSlashIcon class="w-4 h-4" /> {{ t("close") }}
								</button>
								<button
									@click="showFullscreen(log.uuid)"
									class="btn btn-sm btn-outline btn-primary"
								>
									<ArrowsPointingOutIcon class="w-4 h-4" />
								</button>
								<button
									@click="deleteLog(log.uuid)"
									class="btn btn-sm btn-outline btn-error"
									:disabled="deletingLogs.has(log.uuid)"
								>
									<span
										v-if="deletingLogs.has(log.uuid)"
										class="loading loading-spinner loading-xs"
									></span>
									<TrashIcon v-else class="w-4 h-4" />
								</button>
							</div>
						</div>

						<div
							v-if="opened.includes(log.uuid)"
							class="p-4 max-h-[500px] overflow-auto bg-white"
						>
							<LogContent :log="log" :loading="getLoading == log.uuid" />
						</div>
					</div>
				</transition-group>
			</div>

			<!-- 无日志状态 -->
			<div v-if="!loading && logs.length == 0" class="text-center py-8">
				<p class="text-xl text-gray-400">
					{{ t("no-logs") }}
				</p>
			</div>

			<!-- 分页 -->
			<div class="mt-6">
				<component_wait>
					<Page
						:page="page"
						:pageSize="pageSize"
						:total="logCount"
						@change="(e) => (page = e)"
					></Page>
				</component_wait>
			</div>
		</div>

		<!-- 全屏日志弹窗 -->
		<teleport to="body">
			<div
				v-if="fullscreenLogId !== null"
				class="fixed inset-0 z-500 bg-white overflow-auto"
				v-motion-pop-visible
			>
				<div
					class="max-w-4xl mx-auto p-4 sticky top-0 bg-white z-10 shadow-md rounded-lg rounded-t-none"
				>
					<div class="flex justify-between items-center">
						<h2 class="text-xl font-bold truncate flex-1 mr-4">
							{{
								logs.find((log) => log.uuid === fullscreenLogId)?.workflow
									?.name || t("unnamed-project")
							}}
						</h2>
						<button @click="closeFullscreen" class="btn btn-sm flex-shrink-0">
							{{ t("close") }}
						</button>
					</div>
				</div>
				<div class="max-w-4xl mx-auto p-4">
					<div v-if="getLoading == fullscreenLogId" class="text-center py-8">
						<span class="loading loading-spinner loading-lg"></span>
					</div>
					<div
						v-else-if="logs.find((log) => log.uuid === fullscreenLogId)"
						class="space-y-6"
					>
						<LogContent :log="logs.find(log => log.uuid === fullscreenLogId)!" />
					</div>
				</div>
			</div>
		</teleport>
	</div>
</template>

<style scoped>
	.slide-fade-enter-active,
	.slide-fade-leave-active {
		transition: all 0.5s ease;
	}
	.slide-fade-enter-from,
	.slide-fade-leave-to {
		transform: translateX(100%);
		opacity: 0;
	}
</style>
