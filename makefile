# 定义变量
IMAGE_NAME = flowai
IMAGE_TAG = latest
CONTAINER_NAME = flowai
PORT = 80

# 默认目标
.PHONY: all
all: build

# 构建Docker镜像
.PHONY: build
build:
	docker build --platform linux/amd64 -t $(IMAGE_NAME):$(IMAGE_TAG) .

push:
	docker tag $(IMAGE_NAME):$(IMAGE_TAG) uhub.service.ucloud.cn/flowai/$(IMAGE_NAME):$(IMAGE_TAG)
	docker push uhub.service.ucloud.cn/flowai/$(IMAGE_NAME):$(IMAGE_TAG)

# 运行Docker容器
.PHONY: run
run:
	docker run -d -p $(PORT):80 --name $(CONTAINER_NAME) $(IMAGE_NAME):$(IMAGE_TAG)

# 停止并删除Docker容器
.PHONY: stop
stop:
	docker stop $(CONTAINER_NAME)
	docker rm $(CONTAINER_NAME)

# 删除Docker镜像
.PHONY: clean
clean:
	docker rmi $(IMAGE_NAME):$(IMAGE_TAG)

# 重新构建并运行
.PHONY: rebuild
rebuild: stop clean build run

# 显示容器日志
.PHONY: logs
logs:
	docker logs $(CONTAINER_NAME)

# 进入容器shell
.PHONY: shell
shell:
	docker exec -it $(CONTAINER_NAME) /bin/sh

# 帮助信息
.PHONY: help
help:
	@echo "可用的 make 命令："
	@echo "  make build    - 构建 Docker 镜像"
	@echo "  make run      - 运行 Docker 容器"
	@echo "  make stop     - 停止并删除 Docker 容器"
	@echo "  make clean    - 删除 Docker 镜像"
	@echo "  make rebuild  - 重新构建并运行容器"
	@echo "  make logs     - 显示容器日志"
	@echo "  make shell    - 进入容器 shell"
	@echo "  make help     - 显示此帮助信息"