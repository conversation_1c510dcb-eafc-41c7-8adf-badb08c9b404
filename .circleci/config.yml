version: 2.1

jobs:
    build:
        machine:
            image: ubuntu-2204:2024.05.1
        resource_class: medium
        steps:
            - checkout
            - run:
                  name: 检查 VERSION 文件是否有变化
                  command: |
                      if git diff --quiet HEAD^ HEAD -- VERSION; then
                        echo "VERSION 文件没有变化。跳过构建。"
                        circleci-agent step halt
                      else
                        echo "VERSION 文件有变化。继续构建。"
                      fi
            - run:
                  name: 登录到 UCloud Docker 仓库
                  command: |
                      echo "$UCLOUD_DOCKER_PASSWORD" | docker login uhub.service.ucloud.cn -u "$UCLOUD_DOCKER_USERNAME" --password-stdin
            - run:
                  name: 构建 Docker 镜像
                  command: |
                      make build
            # - run:
            #       name: 从 Docker 容器中提取 dist 目录
            #       command: |
            #           docker create --name temp-container flowai
            #           docker cp temp-container:/usr/share/nginx/html/assets/ ./assets
            #           docker rm temp-container
            # - run:
            #       name: 安装 ossutil
            #       command: |
            #           wget http://gosspublic.alicdn.com/ossutil/1.7.7/ossutil64 -O ./ossutil
            #           chmod 755 ./ossutil
            # - run:
            #       name: 配置 ossutil
            #       command: |
            #           ./ossutil config -e ${OSS_ENDPOINT} -i ${OSS_ACCESS_KEY_ID} -k ${OSS_ACCESS_KEY_SECRET}
            # - run:
            #       name: 上传 assets 目录到 OSS
            #       command: |
            #           ./ossutil cp -r -f ./assets oss://${OSS_BUCKET_NAME}/assets
            - run:
                  name: 推送 Docker 镜像
                  command: |
                      make push
workflows:
    version: 2
    check-and-build-workflow:
        jobs:
            - build:
                  filters:
                      branches:
                          only: main
